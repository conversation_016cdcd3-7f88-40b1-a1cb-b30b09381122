import { onUnmounted, onMounted } from 'vue';
import { getVisibilityState, visibilityChange } from '@/utils';
import { userCutScreenDialog } from '@/utils/system-notification-dialog';
import API from '@/services/api/index';

export default function ({ seqId, examId }: { seqId: string; examId: string }) {
    function formatFormData(params: any) {
        const blob = new Blob([JSON.stringify(params)], {
            type: 'application/json;charset=utf-8',
        });
        return blob;
    }

    function browserClosedSend(params: any) {
        const formData = formatFormData({
            encryptExamId: examId,
            ...params,
        });

        const postHeartBeat = formatFormData({
            encSeqId: seqId || '', // 加密大场次id（（考试和测评都使用））
            encExamId: seqId, // 加密小考试ID（考试和测评都使用）
            dataSource: 2, // 写死参数 表示当前环境是 H5
            type: 1, // 写死参数 表示动作是离开
            answerQuestion: true, // 写死参数 表示当前为答题页
        });

        navigator.sendBeacon(API.common.postSwitchScreen, formData);
        navigator.sendBeacon(API.common.postHeartBeat, postHeartBeat);
    }

    async function handleVisibilityChange() {
        const switchType = getVisibilityState() === 'hidden' ? 0 : 1;

        const res = await Invoke.common.postSwitchScreen(
            {
                encryptExamId: seqId,
                switchType,
            },
            { noErrorToast: true },
        );

        if (res.code === 104) {
            userCutScreenDialog();
        }
    }

    onMounted(() => {
        if (visibilityChange) {
            document.addEventListener(visibilityChange, handleVisibilityChange);
        }

        window.addEventListener('beforeunload', () => {
            browserClosedSend({
                switchType: 0, // 写死参数 表示动作是离开
            });
        });
    });

    onUnmounted(() => {
        if (visibilityChange) {
            document.removeEventListener(visibilityChange, handleVisibilityChange);
        }
    });
}
