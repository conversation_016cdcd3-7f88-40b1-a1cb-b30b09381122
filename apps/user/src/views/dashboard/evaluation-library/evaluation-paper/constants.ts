import type { IReportItemNormallized, IReportVersionInfoItem, ReportTypeEnumCombined, TemplateData } from '@/services/api/type';
import { questionMethodEnum, ReportTypeEnum_7, ReportTypeEnum_9, ReportTypeEnum_13 } from '@/services/api/type';
import { reportTemplateDetail } from './useReportTemplate';
import { selectTemplateDetail } from './useTemplate';

export enum PaperStatusEnum {
    启用 = 0,
    停用 = 1,
}
export const statusList = [
    { label: '全部', value: -1 },
    { label: '启用', value: PaperStatusEnum.启用 },
    { label: '停用', value: PaperStatusEnum.停用 },
];
export const statusConfig = {
    [PaperStatusEnum.启用]: { backgroundColor: 'var(--status-success-bg-color)', pointColor: 'var(--status-success-primary-color)', text: '启用' },
    [PaperStatusEnum.停用]: { backgroundColor: 'var(--status-disable-bg-color)', pointColor: 'var(--status-disable-primary-color)', text: '停用' },
};

export enum PageModeEnum {
    ADD,
    COPYADD,
    EDIT,
}
export const watermarkOptions = [
    { label: '需要水印', value: 1 },
    { label: '不需要水印', value: 2 },
];

export interface IExamineeFieldData {
    reportType?: string;
    code: number;
    name: string;
    checked: 0 | 1;
    require: 0 | 1;
    canChanged: 0 | 1;
}
export interface IGroupReferData {
    groupRefer: { id: number; createTime: number; updateTime: number; templateId: number; referenceName: string; createUserId: number; updateUserId: number }[];
}

// export const getReportTypeOptionsAll = ((eunm) =>
//     Object.entries(eunm)
//         .filter(([key, value]) => typeof value === 'number')
//         .map(([label, value]) => ({ value, label: `${label}版` })))(ReportTypeEnum_9);

export function getReportTypeOptionsAll(productId: number) {
    let reportEnum;
    switch (productId) {
        case 7:
            reportEnum = ReportTypeEnum_7;
            break;
        case 9:
            reportEnum = ReportTypeEnum_9;
            break;
        case 13:
            reportEnum = ReportTypeEnum_13;
            break;
        default:
            reportEnum = ReportTypeEnum_9;
            break;
    }
    return Object.entries(reportEnum)
        .filter(([key, value]) => typeof value === 'number')
        .map(([label, value]) => ({ value, label }));
}
export const questionMethod = new Proxy(questionMethodEnum, {
    get(target, prop: any) {
        if (prop === 'list') {
            return Object.entries(target)
                .filter(([key, value]) => typeof value === 'number')
                .map(([label, value]) => ({ value, label }));
        } else {
            return target[prop];
        }
    },
}) as typeof questionMethodEnum & { list: { value: number; label: string }[] };
export interface IComplexRequiredExamineeInfoItem {
    /**
     * 报告类型
     */
    reportType: ReportTypeEnumCombined;
    /**
     * 必选的考生信息，选其一即可
     */
    requiredArray: Array<number>;
}
type requiredExamineeInfo = Array<IComplexRequiredExamineeInfoItem> | Array<number>;
// 产品属性
export interface IProductProperty {
    productId: number;
    productName: string;
    /**
     * 试卷总步数
     */
    productPaperStep: Array<number>;
    /**
     * 是否只能依赖admin模板才能配置试卷
     */
    relayOnTemplate: boolean;
    /**
     * 默认报告标题，非报告版本名称
     */
    defaultReportName: string;
    /**
     * 默认报告副标题
     */
    defaultReportSubName: string;
    /**
     * 是否有 报告副标题 字段
     */
    hasReportSubTitle: boolean;
    /**
     * 必选的模块，选其一即可
     */
    requiredReportModules: Array<string>;
    /**
     * 必选模块有缺时的提示
     */
    requiredReportModulesWarningMessage: string;
    /**
     * 必选的考生信息。如果是数字数组，直接根据数组判断；如果是对象数组，则根据对象的reportType取requiredArray判断。都是选其一即可
     */
    requiredExamineeInfo: requiredExamineeInfo;
    /**
     * 是否有 报告类型 字段
     */
    hasReportType: boolean;
    /**
     * 是否从测评模板中取指定的报告类型
     */
    takeReportTypeFromTemplate: boolean;
    /**
     * 是否在表单中显示 报告类型 字段
     */
    showReportType: boolean;
    /**
     * 是否在表单中禁用 报告类型 字段
     */
    disableReportTypeInSingleForm: boolean;
    /**
     * 报告类型过滤方法
     */
    reportTypeFilterMethod?: (list: { value: ReportTypeEnumCombined; label: string }[]) => { value: ReportTypeEnumCombined; label: string }[];
    /**
     * 默认报告类型
     */
    defaultReportType?: ReportTypeEnumCombined | ((list: { value: ReportTypeEnumCombined; label: string }[]) => ReportTypeEnumCombined);
    /**
     * 是否从admin报告模板中取报告标题与报告副标题
     */
    takeTitlesFromAdmin: boolean;
    /**
     * 报告主副标题赋值方法
     */
    setReportNameFn?: (...args: any) => void;
    /**
     * 是否不自动创建出完整版
     */
    disableGenDefault: boolean;
    /**
     * 是否在选中测评模板后，创建出完整版报告
     */
    createDefaultReportAfterSelectTemplate: () => boolean;
    /**
     * 需要维度
     */
    needDimension: boolean;
    /**
     * 需要维度名称与权重配置表
     */
    needDimensionTableV2?: boolean;
    /**
     * 需要指导语字段——仅影响预览
     */
    needInstruction: boolean;
    /**
     * 需要题目顺序字段——仅影响预览
     */
    needQuestionOrder: boolean;
    /**
     * 需要选项顺序字段——仅影响预览
     */
    needOptionOrder: boolean;
    /**
     * 需要回看规则字段——仅影响预览
     */
    needReviewRule: boolean;
    /**
     * 需要报告参考性题目字段
     */
    needReportReferenceOrderType: boolean;
    /**
     * 需要出题方式字段
     */
    needQuestionMethod: boolean;
    /**
     * 需要维度名称与权重配置表
     */
    needDimensionTable: boolean;
    /**
     * 是否显示 匹配岗位
     */
    showMatchJob?: (...args: any) => boolean;
    /**
     * 是否显示 关键潜在素质
     * @param args
     * @returns
     */
    showKeyPotentialQuality?: (...args: any) => boolean;
    /**
     * 是否显示 报告标题（封面展示的主标题）
     */
    showReportTitle: boolean;
    /**
     * 是否显示 LOGO（封面展示的LOGO）
     */
    showReportLogo: boolean;
    /**
     * 是否允许删除第一个报告版本
     */
    allowDeleteFirstReport: boolean;
    /**
     * 是否允许删除唯一一个报告版本
     */
    allowDeleteOnlyReport: boolean;
}
export const productProperty: Array<IProductProperty> = [
    {
        productId: 1,
        productName: '胜任力测评',
        productPaperStep: [1, 2],
        relayOnTemplate: false,
        defaultReportName: '胜任力测评报告',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4', '5'],
        requiredReportModulesWarningMessage: '综合结果、详细结果、面试建议等3个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: false,
        disableGenDefault: false,
        createDefaultReportAfterSelectTemplate: () => false,
        hasReportSubTitle: false,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 2,
        productName: '职业心理健康筛查测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '职业心理健康筛查报告',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: false,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 3,
        productName: '五维性格测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '五维性格测评报告',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: false,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 4,
        productName: '职业韧性测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 5,
        productName: '领导力测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '领导力情景测评报告',
        defaultReportSubName: 'Leadership Situational Assessment，LSA',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: true,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 6,
        productName: '金融胜任力',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: true,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 7,
        productName: '16T',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: [],
        requiredReportModulesWarningMessage: '',
        requiredExamineeInfo: [-1, -7],
        hasReportType: true,
        takeReportTypeFromTemplate: false,
        showReportType: true,
        disableReportTypeInSingleForm: true,
        reportTypeFilterMethod: (list) => {
            if (!selectTemplateDetail.value?.reportTypeList) {
                return [];
            }
            return list.filter((x) => (selectTemplateDetail.value as TemplateData).reportTypeList.includes(x.value));
        },
        defaultReportType: (list) => list[0]?.value,
        takeTitlesFromAdmin: true,
        setReportNameFn: (reportType: ReportTypeEnumCombined, reportVersionItem: IReportItemNormallized | IReportVersionInfoItem) => {
            if (reportTemplateDetail.value.get(7)?.reportTypeConfig) {
                for (const key in reportTemplateDetail.value.get(7)?.reportTypeConfig) {
                    if (Object.prototype.hasOwnProperty.call(reportTemplateDetail.value.get(7)?.reportTypeConfig, key)) {
                        const __reportTypeConfig__ = reportTemplateDetail.value.get(7)?.reportTypeConfig[key];
                        if (__reportTypeConfig__.reportType === reportType) {
                            reportVersionItem.reportTitle = __reportTypeConfig__.templateName;
                            reportVersionItem.extraParam.templateSubTitle = __reportTypeConfig__.reportSubTitle;
                        }
                    }
                }
            }
        },
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showMatchJob: (reportType) => {
            return reportType === ReportTypeEnum_7.岗位匹配版;
        },
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 8,
        productName: '五维性格测评W版',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4', '7'],
        requiredReportModulesWarningMessage: '综合结果、详细结果、匹配岗位3个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showMatchJob: (reportType) => true,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 9,
        productName: '职业心理健康测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [
            {
                reportType: ReportTypeEnum_9.个人版,
                requiredArray: [-1, -7],
            },
            {
                reportType: ReportTypeEnum_9.团队版,
                requiredArray: [],
            },
            {
                reportType: ReportTypeEnum_9.招聘版,
                requiredArray: [-1, -7],
            },
        ],
        hasReportType: true,
        takeReportTypeFromTemplate: true,
        showReportType: true,
        disableReportTypeInSingleForm: true,
        reportTypeFilterMethod: (list) => {
            if (!selectTemplateDetail.value?.reportTypeList) {
                return [];
            }
            return list.filter((x) => (selectTemplateDetail.value as TemplateData).reportTypeList.includes(x.value));
        },
        defaultReportType: ReportTypeEnum_9.团队版,
        takeTitlesFromAdmin: true,
        setReportNameFn: (reportType: ReportTypeEnumCombined, reportVersionItem: IReportItemNormallized | IReportVersionInfoItem) => {
            if (reportTemplateDetail.value.get(9)?.reportTypeConfig) {
                for (const key in reportTemplateDetail.value.get(9)?.reportTypeConfig) {
                    if (Object.prototype.hasOwnProperty.call(reportTemplateDetail.value.get(9)?.reportTypeConfig, key)) {
                        const __reportTypeConfig__ = reportTemplateDetail.value.get(9)?.reportTypeConfig[key];
                        if (__reportTypeConfig__.reportType === reportType) {
                            reportVersionItem.reportTitle = __reportTypeConfig__.templateName;
                            reportVersionItem.extraParam.templateSubTitle = __reportTypeConfig__.reportSubTitle;
                        }
                    }
                }
            }
        },
        hasReportSubTitle: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: true,
        allowDeleteOnlyReport: true,
    },
    {
        productId: 10,
        productName: '高阶思维能力HOT',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: false,
        needInstruction: false,
        needQuestionOrder: false,
        needOptionOrder: false,
        needReviewRule: false,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 11,
        productName: '工作风格测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 12,
        productName: '高潜人才识别测评（HIPO）',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: true,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 13,
        productName: 'GBA立体人才画像',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: true,
        takeReportTypeFromTemplate: false,
        showReportType: true,
        disableReportTypeInSingleForm: true,
        reportTypeFilterMethod: (list) => {
            if (!selectTemplateDetail.value?.reportTypeList) {
                return [];
            }
            return list.filter((x) => (selectTemplateDetail.value as TemplateData).reportTypeList.includes(x.value));
        },
        defaultReportType: (list) => list[0]?.value,
        takeTitlesFromAdmin: true,
        setReportNameFn: (reportType: ReportTypeEnumCombined, reportVersionItem: IReportItemNormallized | IReportVersionInfoItem) => {
            if (reportTemplateDetail.value.get(13)?.reportTypeConfig) {
                for (const key in reportTemplateDetail.value.get(13)?.reportTypeConfig) {
                    if (Object.prototype.hasOwnProperty.call(reportTemplateDetail.value.get(13)?.reportTypeConfig, key)) {
                        const __reportTypeConfig__ = reportTemplateDetail.value.get(13)?.reportTypeConfig[key];
                        if (__reportTypeConfig__.reportType === reportType) {
                            reportVersionItem.reportTitle = __reportTypeConfig__.templateName;
                            reportVersionItem.extraParam.templateSubTitle = __reportTypeConfig__.reportSubTitle;
                        }
                    }
                }
            }
        },
        hasReportSubTitle: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        needDimension: false,
        needInstruction: false,
        needQuestionOrder: false,
        needOptionOrder: false,
        needReviewRule: false,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 14,
        productName: '敬业度调研',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [],
        hasReportType: false,
        takeReportTypeFromTemplate: true,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        hasReportSubTitle: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 15,
        productName: '笔迹职业适应力测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 16,
        productName: '认知能力测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4'],
        requiredReportModulesWarningMessage: '综合结果、详细结果2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: true,
        needDimensionTable: true,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 17,
        productName: '职业动机测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '15', '17'],
        requiredReportModulesWarningMessage: '综合结果、职业动机概览、职业锚类型3个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 18,
        productName: '校招方案组合',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '校招方案组合报告',
        defaultReportSubName: '',
        requiredReportModules: ['3', '5'],
        requiredReportModulesWarningMessage: '综合结果、面试建议2个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -140],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: false,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: false,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: false,
        showReportLogo: false,
        allowDeleteFirstReport: true,
        allowDeleteOnlyReport: true,
    },
    {
        productId: 19,
        productName: '新质人才测评方案',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '新质人才测评方案报告',
        defaultReportSubName: '',
        requiredReportModules: [],
        requiredReportModulesWarningMessage: '',
        requiredExamineeInfo: [-1, -140],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: true,
        allowDeleteOnlyReport: true,
    },
    {
        productId: 20,
        productName: '政治素养测评',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '政治素养测评报告',
        defaultReportSubName: '',
        requiredReportModules: ['1', '2', '3', '6'],
        requiredReportModulesWarningMessage: '封面、报告说明、综合结果、附录4个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        needDimensionTable: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: false,
        needQuestionOrder: false,
        needOptionOrder: false,
        needReviewRule: false,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTableV2: true,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
    {
        productId: 21,
        productName: '五维性格测评0.2',
        productPaperStep: [2],
        relayOnTemplate: true,
        defaultReportName: '',
        defaultReportSubName: '',
        requiredReportModules: ['3', '4', '7'],
        requiredReportModulesWarningMessage: '综合结果、详细结果、匹配岗位3个模块，请至少选择1个',
        requiredExamineeInfo: [-1, -7],
        hasReportType: false,
        takeReportTypeFromTemplate: false,
        showReportType: false,
        disableReportTypeInSingleForm: false,
        reportTypeFilterMethod: undefined,
        takeTitlesFromAdmin: true,
        disableGenDefault: true,
        createDefaultReportAfterSelectTemplate: () => true,
        hasReportSubTitle: true,
        needDimension: true,
        needInstruction: true,
        needQuestionOrder: true,
        needOptionOrder: true,
        needReviewRule: true,
        needReportReferenceOrderType: false,
        needQuestionMethod: false,
        needDimensionTable: false,
        showMatchJob: (reportType) => true,
        showKeyPotentialQuality: (reportType) => true,
        showReportTitle: true,
        showReportLogo: true,
        allowDeleteFirstReport: false,
        allowDeleteOnlyReport: false,
    },
];
