<template>
    <div class="report-single-container">
        <b-form ref="singleReportFormRef" :model="value" class="report-single-form" layout="vertical" gentleValidation>
            <b-form-item
                :field="`versionInfo[${currentReportVersionIndex}].reportVersionList.reportName`"
                :rules="[{ required: true, message: '请填写报告名称' }, { validator: reportNameValidator }]"
                asteriskPosition="end"
                label="报告版本名称"
                required
            >
                <b-input
                    v-model.trim="value.versionInfo[currentReportVersionIndex].reportVersionList.reportName"
                    size="large"
                    :disabled="formDisabled"
                    placeholder="请输入"
                    trimBeforePaste
                    showWordLimit
                    :maxLength="20"
                />
            </b-form-item>
            <b-form-item
                v-if="selectedProductProperty.hasReportType && selectedProductProperty.showReportType"
                asteriskPosition="end"
                label="报告类型"
                required
                style="margin-bottom: 0"
            >
                <b-radio-group
                    v-model="value.versionInfo[currentReportVersionIndex].extraParam.reportType"
                    :disabled="formDisabled || selectedProductProperty.disableReportTypeInSingleForm"
                    :options="reportTypeOptions"
                    @change="handleReportTypeChange"
                />
            </b-form-item>
            <b-form-item
                v-if="selectedProductProperty?.productId !== 5"
                :field="`versionInfo[${currentReportVersionIndex}].reportVersionList.moduleCodes`"
                :rules="[{ validator: moduleCodesValidator }]"
            >
                <Shuttle
                    :moduleCodes="localModuleCodes"
                    :disabled="formDisabled"
                    :selectedProductProperty="selectedProductProperty"
                    :reportType="value.versionInfo[currentReportVersionIndex].extraParam.reportType"
                    @update:moduleCodes="updateModuleCodes"
                />
            </b-form-item>
            <b-form-item
                v-if="selectedProductProperty.showReportTitle"
                :field="`versionInfo[${currentReportVersionIndex}].reportTitle`"
                :rules="[{ required: true, message: '请填写报告标题' }]"
                asteriskPosition="end"
                label="报告标题"
                required
            >
                <b-input
                    v-model.trim="value.versionInfo[currentReportVersionIndex].reportTitle"
                    size="large"
                    :disabled="formDisabled"
                    placeholder="显示在报告封面的标题名称"
                    trimBeforePaste
                    showWordLimit
                    :maxLength="30"
                />
            </b-form-item>
            <b-form-item v-if="selectedProductProperty.hasReportSubTitle" asteriskPosition="end" label="报告副标题">
                <b-input
                    v-model.trim="value.versionInfo[currentReportVersionIndex].extraParam.templateSubTitle"
                    size="large"
                    :disabled="formDisabled"
                    placeholder="显示在报告封面的副标题名称"
                    trimBeforePaste
                    showWordLimit
                    :maxLength="100"
                />
            </b-form-item>
            <b-form-item v-if="selectedProductProperty.showReportLogo" asteriskPosition="end" label="LOGO">
                <AvatarUpload v-model="value.versionInfo[currentReportVersionIndex].reportLogo" :disabled="formDisabled" />
            </b-form-item>
            <b-form-item
                :field="`versionInfo[${currentReportVersionIndex}].reportExamineeField`"
                :rules="[{ required: requiredExamineeFieldCode.length > 0, message: '请选择考生信息' }, { validator: reportExamineeFieldValidator }]"
                asteriskPosition="end"
                label="考生信息"
            >
                <b-checkbox-group
                    :disabled="formDisabled"
                    :modelValue="value.versionInfo[currentReportVersionIndex].reportExamineeField.map((x) => x.code)"
                    :options="examineeFieldOptions"
                    @update:modelValue="handleExamineeFieldChange"
                />
            </b-form-item>
            <b-form-item
                v-if="selectedProductProperty.showKeyPotentialQuality?.(value.versionInfo[currentReportVersionIndex].extraParam.reportType)"
                asteriskPosition="end"
                label="关键潜在素质"
                required
            >
                <PotentialQualityMultiple
                    ref="matchJobRef"
                    v-model="value.versionInfo[currentReportVersionIndex].extraParam!.keyPotentialQualityList"
                    :productId="value.encProductId"
                    :formData="value"
                    :disabled="formDisabled"
                />
            </b-form-item>
            <b-form-item
                v-if="selectedProductProperty.showMatchJob?.(value.versionInfo[currentReportVersionIndex].extraParam.reportType)"
                asteriskPosition="end"
                label="匹配岗位"
                required
            >
                <MatchJobMultiple
                    v-if="[PCode.OEWA, PCode.OEWA2].includes(selectedProductProperty?.productId)"
                    ref="matchJobRef"
                    v-model="value.versionInfo[currentReportVersionIndex].extraParam!.matchJobList"
                    :productId="value.encProductId"
                    :formData="value"
                    :disabled="formDisabled"
                />
                <MatchJob v-else ref="matchJobRef" :encProductId="encProductId" :formData="value.versionInfo[currentReportVersionIndex]" :disabled="formDisabled" />
            </b-form-item>
            <b-form-item asteriskPosition="end" label="报告水印" required>
                <b-radio-group v-model="value.versionInfo[currentReportVersionIndex].watermark" :disabled="formDisabled" :options="watermarkOptions" />
            </b-form-item>
            <b-form-item
                v-if="[1].includes(selectedProductProperty?.productId)"
                :field="`versionInfo[${currentReportVersionIndex}].reportDimensionField`"
                :rules="[{ validator: reportDimensionFieldValidator }]"
                asteriskPosition="end"
                label="显示配置"
                required
            >
                <b-table
                    :tableData="value.versionInfo[currentReportVersionIndex].reportDimensionField"
                    :columns="reportDimensionFieldColmun"
                    nullCellDisplay=""
                    fullHeight
                    stickyHeader
                    :border="false"
                    :scroll="{ x: '100%' }"
                    tableLayoutFixed
                    style="margin-bottom: 20px"
                >
                    <template #td-showName="{ raw }">
                        <b-input v-model.trim="raw.showName" size="large" :disabled="formDisabled" trimBeforePaste showWordLimit :maxLength="30" />
                    </template>
                </b-table>
            </b-form-item>
            <b-form-item v-if="[1].includes(selectedProductProperty?.productId)" label="群体参考值" asteriskPosition="end">
                <template #label>
                    群体参考值
                    <b-tooltip content="群体参照值是指特定人群在所测维度上的普遍水平，如果需要在报告中显示，请点选对应的群体名称">
                        <SvgIcon width="16" height="16" name="question" />
                    </b-tooltip>
                </template>
                <b-select
                    :disabled="formDisabled"
                    :modelValue="value.versionInfo[currentReportVersionIndex].reportGroupReferField[0]?.code"
                    :options="referFieldOptions"
                    allowClear
                    @update:modelValue="handleGroupUpdate"
                />
            </b-form-item>
        </b-form>
    </div>
</template>

<script setup lang="ts">
import { PCode } from '@crm/biz-exam-product';
import type { IInfo } from '@/services/api/type';
import type { IComplexRequiredExamineeInfoItem, IExamineeFieldData, IGroupReferData, IProductProperty, PageModeEnum } from './constants';
import { computed, onMounted, ref, watch } from 'vue';
import Shuttle from '../dialog/shuttle.vue';
import AvatarUpload from './avatar-upload.vue';
import { watermarkOptions } from './constants';
import MatchJobMultiple from './match-job-multiple.vue';
import PotentialQualityMultiple from './potential-quality-multiple.vue';
import MatchJob from './match-job.vue';
import { useReportTypeList } from './useReportTypeList';
import { useValidateReportForm } from './useValidateReportForm';

defineOptions({
    name: 'SingleReportForm',
});

const props = defineProps<IProps>();

interface IProps {
    value: IInfo;
    selectedProductProperty: IProductProperty;
    pageMode: PageModeEnum;
    isLinkExam: boolean;
    encProductId: string;
    plainProductId: number;
    examineeFieldData: Array<IExamineeFieldData>;
    groupReferData: IGroupReferData;
    currentReportVersionIndex: number;
}

const singleReportFormRef = ref();
const matchJobRef = ref();
const { setRef, resetRef } = useValidateReportForm();
resetRef();

const { reportTypeOptions } = await useReportTypeList(props.selectedProductProperty);

const formDisabled = computed(() => Boolean(props.value.versionInfo[props.currentReportVersionIndex].reportVersionList.disabled));
const reportDimensionFieldColmun = ref([
    {
        label: '测评维度',
        field: 'dimName',
        minWidth: 110,
    },
    {
        label: '展示名称',
        field: 'showName',
        minWidth: 90,
    },
]);
const examineeFieldOptions = computed(() => {
    const result = props.examineeFieldData
        .filter((i) => props.value.versionInfo[props.currentReportVersionIndex].extraParam.reportType == i.reportType || i.reportType === undefined)
        .map((x) => {
            return {
                label: x.name,
                value: x.code,
                disabled: !x.canChanged,
                default: x.checked,
            };
        });
    return result;
});

// 重置
function handleReportTypeChange() {
    props.value.versionInfo[props.currentReportVersionIndex].reportExamineeField = examineeFieldOptions.value
        .filter((i) => i.default === 1)
        .map((i) => {
            return {
                code: i.value,
                name: i.label,
            };
        });
}

function handleExamineeFieldChange(v: number[]) {
    props.value.versionInfo[props.currentReportVersionIndex].reportExamineeField = v.map((x) => {
        return {
            code: x,
            name: examineeFieldOptions.value.find((y: any) => y.value === x)?.label ?? '',
        };
    });
}

function handleGroupUpdate(v: number | string) {
    if (v !== '') {
        props.value.versionInfo[props.currentReportVersionIndex].reportGroupReferField = [
            {
                code: Number(v),
                name: referFieldOptions.value.find((y) => y.value === v)?.label ?? '',
            },
        ];
    } else {
        props.value.versionInfo[props.currentReportVersionIndex].reportGroupReferField = [];
    }
}

const referFieldOptions = computed(() => {
    return props.groupReferData.groupRefer.map((x) => {
        return {
            label: x.referenceName,
            value: x.id,
        };
    });
});

const localModuleCodes = ref<string[]>([]);
watch(
    () => props.currentReportVersionIndex,
    () => {
        localModuleCodes.value = JSON.parse(props.value.versionInfo[props.currentReportVersionIndex].reportVersionList.moduleCodes).map((y: number) => String(y));
    },
    { immediate: true }
);

function updateModuleCodes(value: string[]) {
    localModuleCodes.value = value;
    props.value.versionInfo[props.currentReportVersionIndex].reportVersionList.moduleCodes = JSON.stringify(localModuleCodes.value.map((x) => Number(x)));
}

type callbackType = (error?: string | undefined) => void;
function reportNameValidator(value: string | undefined, callback: callbackType) {
    if (value) {
        const existedNames = props.value.versionInfo.filter((x, i) => i !== props.currentReportVersionIndex).map((x) => x.reportVersionList.reportName);
        if (existedNames.includes(value)) {
            callback('报告版本名称已存在，请重新填写');
        } else {
            callback();
        }
    } else {
        callback('请填写报告名称');
    }
}
function moduleCodesValidator(value: string, callback: callbackType) {
    const moduleCodes = JSON.parse(value).map((x: number) => String(x));
    if (props.selectedProductProperty.requiredReportModules.length === 0 || moduleCodes.some((x: any) => props.selectedProductProperty.requiredReportModules.includes(x))) {
        callback();
    } else {
        callback(props.selectedProductProperty.requiredReportModulesWarningMessage);
    }
}

const requiredExamineeFieldCode = computed<number[]>(() => {
    let result: number[] = [];
    if (typeof props.selectedProductProperty.requiredExamineeInfo[0] === 'number') {
        result = props.selectedProductProperty.requiredExamineeInfo as number[];
    } else {
        result =
            (
                props.selectedProductProperty.requiredExamineeInfo.find(
                    (x) => (x as IComplexRequiredExamineeInfoItem).reportType === props.value.versionInfo[props.currentReportVersionIndex].extraParam.reportType
                ) as IComplexRequiredExamineeInfoItem
            )?.requiredArray ?? [];
    }
    return result;
});
function reportExamineeFieldValidator(value: { code: number; name: string }[], callback: callbackType) {
    const selectedExamineeFieldCode = value.map((x) => x.code);
    const requiredExamineeFieldName = requiredExamineeFieldCode.value.map((x) => examineeFieldOptions.value.find((y) => y.value === x)?.label);
    if (requiredExamineeFieldCode.value.length === 0) {
        callback();
        return;
    }
    if (requiredExamineeFieldCode.value.some((x) => selectedExamineeFieldCode.includes(x))) {
        callback();
    } else {
        callback(`${requiredExamineeFieldName.join('和')}请至少选择一项`);
    }
}
function reportDimensionFieldValidator(value: { code: string; dimName: string; showName: string }[], callback: callbackType) {
    // 发现空的showName，报 请填写「xxxx维度名称」的展示名
    if (value.some((x) => !x.showName)) {
        callback(`请填写「${value.find((x) => !x.showName)?.dimName}」的展示名`);
        return;
    }
    // 发现重复的showName
    if (value.length !== new Set(value.map((x) => x.showName)).size) {
        callback('显示配置中存在重复的展示名称');
        return;
    }
    callback();
}

onMounted(() => {
    setRef(singleReportFormRef, matchJobRef);
});
</script>
