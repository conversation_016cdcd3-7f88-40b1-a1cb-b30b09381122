import type { IInfo } from '@/services/api/type';
import type { IProductProperty } from './constants';
import { useCommonStore } from '@/views/app-base';
import { productProperty } from './constants';
import { fillDefaultExamineeInfo } from './useExamineeInfo';
import { reportTemplateDetail } from './useReportTemplate';
import { reportModuleList, useReportTypeList } from './useReportTypeList';

export async function createDefaultReport(formData: IInfo, currentProductProperty: IProductProperty, evaluateTemplate?: any) {
    const productList = useCommonStore().productList;
    const currentProductItem = productList.find((x) => x.productId === currentProductProperty.productId);
    const allProductProperties = [currentProductProperty];
    if (currentProductItem?.children) {
        for (let i = 0; i < currentProductItem.children.length; i++) {
            const singleProduct = currentProductItem.children[i];
            allProductProperties.push(productProperty.find((x) => x.productId === singleProduct.productId)!);
        }
    }
    for (let i = 0; i < allProductProperties.length; i++) {
        const singleProductProperty = allProductProperties[i];
        const productItem = productList.find((x) => x.productId === singleProductProperty.productId);
        const { reportTypeOptions } = useReportTypeList(singleProductProperty);

        const createReportVersion = (reportTypeOption?: { label: string; value: any }) => {
            let reportName = `${productItem?.productName}_完整版`;
            if (reportTypeOption) {
                reportName = `${productItem?.productName}_${reportTypeOption.label}_完整版`;
            }

            const newVersion = {
                extraParam: {
                    templateSubTitle: singleProductProperty?.defaultReportSubName ?? '',
                    matchJob: '',
                    matchJobShowName: '',
                    matchJobList: [],
                    keyPotentialQualityList: [],
                    reportType: reportTypeOption?.value,
                },
                encProductId: productItem?.encProductId ?? '',
                reportLogo: '',
                watermark: 1,
                reportTitle: singleProductProperty?.defaultReportName ?? '',
                reportVersionList: {
                    moduleCodes: '[]', // 报告模块
                    reportName, // 报告版本名称
                    disabled: 0,
                },
                reportExamineeField: [],
                reportDimensionField: [],
                reportGroupReferField: [],
            };

            formData.versionInfo.push(newVersion);
            const newIndex = formData.versionInfo.length - 1;

            if (singleProductProperty.hasReportType && !reportTypeOption) {
                let __reportType__;
                if (singleProductProperty.takeReportTypeFromTemplate) {
                    __reportType__ = evaluateTemplate?.reportType;
                }
                if (!__reportType__) {
                    if (typeof singleProductProperty.defaultReportType === 'function') {
                        __reportType__ = singleProductProperty.defaultReportType(reportTypeOptions);
                    } else {
                        __reportType__ = singleProductProperty.defaultReportType;
                    }
                }
                formData.versionInfo[newIndex].extraParam.reportType = __reportType__;
            }

            fillDefaultExamineeInfo(formData.versionInfo[newIndex], singleProductProperty.productId);
            fillReportModules(formData, newIndex, singleProductProperty.productId);
            // 报告主副标题，优先取默认指定，次优先取admin报告模板配置值
            if (singleProductProperty.takeTitlesFromAdmin) {
                if (singleProductProperty.setReportNameFn) {
                    singleProductProperty.setReportNameFn(formData.versionInfo[newIndex].extraParam.reportType, formData.versionInfo[newIndex]);
                } else {
                    formData.versionInfo[newIndex].reportTitle = reportTemplateDetail.value.get(singleProductProperty.productId)?.templateName ?? '';
                    formData.versionInfo[newIndex].extraParam.templateSubTitle = reportTemplateDetail.value.get(singleProductProperty.productId)?.reportSubTitle ?? '';
                }
            }
        };

        if (singleProductProperty.hasReportType) {
            reportTypeOptions.forEach((option) => createReportVersion(option));
        } else {
            createReportVersion();
        }
    }
}

function fillReportModules(formData: IInfo, index: number, productId: number) {
    let moduleList = reportModuleList.value.get(productId)!;
    const type = Object.prototype.toString.call(moduleList);
    if (type === '[object Object]') {
        moduleList = (moduleList as Record<string, { code: string; name: string }[]>)[formData.versionInfo[index].extraParam.reportType!] as { code: string; name: string }[];
    }
    formData.versionInfo[index].reportVersionList.moduleCodes = JSON.stringify((moduleList as { code: string; name: string }[]).map((x) => Number(x.code)));
}
