<template>
    <BackButton />
    <div class="evaluation-paper-detail-page">
        <div class="page-header">
            <div class="page-title">
                <b-title size="large" style="margin-right: 12px">
                    {{ pageTitleMap[pageMode] }}
                </b-title>
                <span class="field-label">测评产品：</span>
                <span class="filed-value">{{ productName }}</span>
            </div>
            <div
                v-if="(currentProductProperty?.productPaperStep?.length ?? 0) > 1"
                style="flex: 1; justify-content: center; display: flex; position: absolute; width: calc(100% - 40px)"
            >
                <b-step class="step-class" :current="step" style="width: 243px">
                    <b-step-item>配置试卷</b-step-item>
                    <b-step-item>配置报告</b-step-item>
                </b-step>
            </div>
            <PreviewPaperEntrance :currentProductProperty="currentProductProperty" :formData="value" @handlePreview="handlePreview" />
        </div>
        <div v-if="!detailLoading">
            <template v-if="step === 1">
                <div style="height: calc(100vh - 119px); overflow: auto">
                    <b-form class="page-content" style="width: 720px" layout="vertical">
                        <div class="page-content-title">基础信息</div>
                        <div style="max-width: 500px">
                            <b-form-item asteriskPosition="end" label="试卷名称" required>
                                <b-input
                                    v-model.trim="value.paperName"
                                    size="large"
                                    :disabled="pageMode === PageModeEnum.EDIT || isLinkExam"
                                    placeholder="请输入"
                                    trimBeforePaste
                                    showWordLimit
                                    :maxLength="30"
                                />
                            </b-form-item>
                            <b-form-item asteriskPosition="end" label="试卷备注">
                                <b-input v-model.trim="value.remark" size="large" placeholder="请输入" :disabled="isLinkExam" trimBeforePaste showWordLimit :maxLength="30" />
                            </b-form-item>
                        </div>
                        <div class="group quick-config">
                            <div class="group" style="gap: 20px">
                                <SvgIcon width="50" height="50" name="template" />
                                <div>
                                    <div class="quick-config-title">快速配置</div>
                                    <div class="quick-config-desc">选择模板可一键填写测评维度与指导语，选择后可自行调整</div>
                                </div>
                            </div>
                            <b-button status="primary" :disabled="isLinkExam" type="outline" size="large" @click="handleOpenTemplate"> 使用模板 </b-button>
                        </div>
                        <b-form-item asteriskPosition="end" label="指导语" placeholder="" required>
                            <b-textarea
                                v-model.trim="value.instruction"
                                placeholder="请填写指导语"
                                showWordLimit
                                :maxLength="200"
                                :autoSize="{
                                    minRows: 4,
                                    maxRows: 4,
                                }"
                                :disabled="isLinkExam"
                            />
                        </b-form-item>
                        <b-form-item asteriskPosition="end" label="测评维度" required>
                            <div class="dimension-button" @click="handleDimensionButtonClick">
                                <template v-if="value.dimensionConfigList.length > 1">
                                    <template v-for="dimension in value.dimensionConfigList" :key="dimension.dimensionId">
                                        <div v-if="dimension.dimensionName !== '总计'" class="dimension-button-item">
                                            {{ dimension.dimensionName }}
                                            <!-- <svg-icon width="10" height="10" @click.stop="() => handleDimensionDelete(dimension)" name="delete"></svg-icon> -->
                                        </div>
                                    </template>
                                </template>
                                <template v-else>
                                    <input class="input-placeholder" readonly placeholder="测评维度" />
                                </template>
                            </div>
                        </b-form-item>
                        <b-table
                            v-if="value.dimensionConfigList.length > 1"
                            :tableData="value.dimensionConfigList"
                            :columns="columns"
                            nullCellDisplay=""
                            fullHeight
                            class="dimension-table"
                            stickyHeader
                            :border="false"
                            :scroll="{ x: '100%' }"
                            tableLayoutFixed
                            style="margin-bottom: 20px"
                        >
                            <template #td-dimensionName="{ raw }">
                                {{ raw.dimensionName }}
                                <template v-if="raw.dimensionName !== '总计' && raw.description">
                                    <b-tooltip :content="raw.description">
                                        <SvgIcon width="16" height="16" name="question" />
                                    </b-tooltip>
                                </template>
                            </template>
                            <template #td-participateScore="{ raw, $index }">
                                <template v-if="raw.dimensionName !== '总计'">
                                    <b-switch
                                        v-if="$index !== 0"
                                        v-model="value.dimensionConfigList[$index].participateScore"
                                        :disabled="isLinkExam"
                                        :checkedValue="1"
                                        :uncheckedValue="0"
                                        @change="() => (value.dimensionConfigList[$index].weight = undefined)"
                                    />
                                </template>
                            </template>
                            <template #td-weight="{ raw, $index }">
                                <template v-if="raw.dimensionName !== '总计'">
                                    <template v-if="value.dimensionConfigList[$index].participateScore">
                                        <b-input-number v-if="$index !== 0" v-model="raw.weight" size="large" :disabled="isLinkExam" :precision="0" hideButton :max="100" :min="1">
                                            <template #suffix> % </template>
                                        </b-input-number>
                                    </template>
                                </template>
                                <div v-else>{{ weightSum }}%</div>
                            </template>
                            <template #td-questionCount="{ raw }">
                                <div v-if="raw.dimensionName !== '总计'">
                                    {{ raw.questionCount }}
                                </div>
                                <span v-else>
                                    {{ questionCountSum }}
                                </span>
                            </template>
                            <template #td-adviseAnswerTime="{ raw }">
                                <span v-if="raw.dimensionName !== '总计'">
                                    {{ raw.adviseAnswerTime }}
                                </span>
                                <span v-else>
                                    {{ dimensionConfigTimeSum }}
                                </span>
                                <span> 分钟</span>
                            </template>
                        </b-table>
                        <div class="page-content-title">作答配置</div>
                        <b-form-item label="题目顺序" required asteriskPosition="end" class="config-radio">
                            <b-radio-group v-model="value.questionOrderType" :disabled="isLinkExam">
                                <b-radio :value="1" :ripple="false"> 同题型内乱序 </b-radio>
                                <b-radio :value="2" :ripple="false"> 固定顺序 </b-radio>
                            </b-radio-group>
                        </b-form-item>
                        <b-form-item label="进入下一题" required asteriskPosition="end" class="config-radio">
                            <b-radio-group v-model="value.nextQuestion" :disabled="isLinkExam">
                                <b-radio :value="1" :ripple="false"> 选择任一选项后自动进入下一题 </b-radio>
                                <b-radio :value="2" :ripple="false"> 手动点击【下一题】按钮 </b-radio>
                            </b-radio-group>
                        </b-form-item>
                        <b-form-item label="选项顺序" required asteriskPosition="end" class="config-radio">
                            <b-radio-group v-model="value.optionOrderType" :disabled="isLinkExam">
                                <b-radio :value="1" :ripple="false"> 选项乱序（量表题型不乱序） </b-radio>
                                <b-radio :value="2" :ripple="false"> 固定顺序 </b-radio>
                            </b-radio-group>
                        </b-form-item>
                        <b-form-item label="回看规则" required asteriskPosition="end" class="config-radio">
                            <b-radio-group v-model="value.reviewRuleType" :disabled="isLinkExam">
                                <b-radio :value="1" :ripple="false"> 可回看一题 </b-radio>
                                <b-radio :value="2" :ripple="false"> 不可回看 </b-radio>
                                <b-radio :value="3" :ripple="false"> 不限制回看 </b-radio>
                            </b-radio-group>
                        </b-form-item>
                    </b-form>
                    <div class="page-footer">
                        <div class="page-footer-content">
                            <b-button status="primary" type="primary" size="large" @click="handleNextStep"> 下一步 </b-button>
                        </div>
                    </div>
                </div>
            </template>
            <template v-else-if="step === 2">
                <div class="report-config-form-wrap" style="height: calc(100vh - 119px); overflow: auto">
                    <ReportConfig
                        :value="value"
                        :currentProductProperty="currentProductProperty"
                        :pageMode="pageMode"
                        :isLinkExam="isLinkExam"
                        :encProductId="encProductId"
                        :plainProductId="plainProductId"
                        :groupReferData="groupReferData"
                        :selectTemplateDetail="selectTemplateDetail"
                        :reportTemplateDetail="reportTemplateDetail"
                    />
                    <div class="page-footer">
                        <div class="page-footer-content" style="padding: 0; max-width: none">
                            <b-button v-if="currentProductProperty?.productPaperStep.length > 1" status="primary" type="outline" @click="handlePreviousStep"> 上一步 </b-button>
                            <b-button :disabled="saveLoading" status="primary" type="primary" size="large" @click="handleSave"> 保存 </b-button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
    <TemplateDialog v-if="dialogOpened" v-model="dialogOpened" :productName="productName" @confirm="handleTemplateConfirm" />
    <DialogDimension
        v-if="dimensionDialogOpened"
        v-model="dimensionDialogOpened"
        :encProductId="value.encProductId"
        :selectedDimension="selectedDimension"
        @success="handleDimensionConfirm"
    />
</template>

<script setup lang="tsx">
import type { IDimensionConfigList, IInfo, IPaperSaveType, TemplateData } from '@/services/api/type';
import type { IGroupReferData, IProductProperty } from './constants';
import { _evaluationPaperDetail, _evaluationPaperSave, _productDimensionList, _reportTemplateGroupRefer } from '@/services/api/evaluation-library';
import { add } from '@/utils/math';
import { BackButton } from '@/views/dashboard/sessions-v2/components';
import { safeParseAsync } from 'valibot';
import { computed, onMounted, ref, watch } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { useLeaveDialog } from '../../sessions/hook/use-leave-dialog';
import DialogDimension from '../dialog/dialog-dimension.vue';
import PreviewPaperEntrance from '../dialog/preview-paper-entrance.vue';
import TemplateDialog from '../dialog/template-dialog.vue';
import { PageModeEnum, productProperty, questionMethod } from './constants';
import ReportConfig from './report-config.vue';
import { PAPER_2_SCHEMA, PAPER_SCHEMA } from './schema';
import { getExamineeInfoMap } from './useExamineeInfo';
import { goPreview } from './usePreviewPaper';
import { useProductList } from './useProductList';
import { useRenameTableValue, useValidateRenameTableForm } from './useRenameDimensionTable';
import { useReportTemplate } from './useReportTemplate';
import { getReportModuleList } from './useReportTypeList';
import { createDefaultReport } from './useReportVersion';
import { useTemplate } from './useTemplate';
import { useValidateReportForm } from './useValidateReportForm';
import { useRenameTableValueV2, useValidateRenameTableFormV2 } from './useRenameDimensionTableV2';

defineOptions({
    name: 'PaperDetail',
});

const { openLeaveDialog, getLeaveControl, setLeaveControl } = useLeaveDialog();

const $route = useRoute();
const $router = useRouter();
const encProductId = computed(() => String($route.query.encProductId ?? ''));
const encPaperId = computed(() => $route.query.encPaperId);
const encCopyFromId = computed(() => $route.query.encCopyFromPaperId);
const isLinkExam = ref(false);

const opened = ref(false);
const dialogOpened = ref(false);
const dimensionDialogOpened = ref(false);

const step = ref(1);

const groupReferData = ref<IGroupReferData>({ groupRefer: [] });
onMounted(async () => {
    await init();
    const { data } = await _reportTemplateGroupRefer({ encProductId: String(encProductId.value) });
    groupReferData.value = data;
});

const value = ref<IInfo>({
    versionInfo: [],
    encryptId: '',
    paperName: '',
    encProductId: String(encProductId.value),
    encTemplateId: '',
    remark: '',
    instruction: '1.请空余出充足的时间进行作答\n2.选择一个不被打扰的安静环境\n3.独立完成测验，且按照自身实际情况如实作答\n4.选项无对错好坏之分，请选择更符合自己的选项',
    questionOrderType: 1,
    optionOrderType: 1,
    reviewRuleType: 1,
    nextQuestion: 2,
    timedRuleType: undefined,
    commitPaperWhenTimeOut: 0,
    dimensionConfigList: [],
    dimensionRenameList: [],
    questionMethod: questionMethod['随机出题（千人千卷）'],
});

// 测评模板列表相关
const { selectTemplateDetail, getTemplateList, SupplementList, initTemplateData, setCurrentProductProperty, getPaperTemplateDetail } = useTemplate(encProductId.value, value.value);
initTemplateData();

// 报告模板相关
const { getReportTemplateDetail, reportTemplateDetail, initReportTemplateDetail } = useReportTemplate(encProductId.value);
initReportTemplateDetail();

const weightSum = computed(() => {
    return value.value.dimensionConfigList
        .filter((i, index) => index !== 0 && i.participateScore === 1)
        .reduce((prev, cur) => {
            return add(prev, cur.weight || 0);
        }, 0);
});

const questionCountSum = computed(() => {
    return Number.parseFloat(
        String(
            value.value.dimensionConfigList.reduce((prev, cur) => {
                return add(prev, cur.questionCount || 0);
            }, 0)
        )
    );
});

const dimensionConfigTimeSum = computed(() => {
    return Number.parseFloat(
        String(
            value.value.dimensionConfigList.reduce((prev, cur) => {
                return add(prev, cur.adviseAnswerTime || 0);
            }, 0)
        )
    );
});

function handleOpenTemplate() {
    dialogOpened.value = true;
}

function handleDimensionButtonClick() {
    if (!isLinkExam.value) {
        dimensionDialogOpened.value = true;
    }
}

function handlePreview() {
    goPreview(value.value, selectTemplateDetail.value, currentProductProperty.value);
}

function handleDimensionConfirm(v: IDimensionConfigList[]) {
    value.value.dimensionConfigList = v.map((x) => {
        return {
            encryptDimensionId: x.encryptDimensionId,
            dimensionName: x.dimensionName,
            participateScore: x.participateScore,
            weight: x.weight,
            questionCount: x.questionCount,
            adviseAnswerTime: x.adviseAnswerTime,
            answerConsistent: x.answerConsistent,
            description: x.description,
        };
    });
    value.value.dimensionConfigList.push({
        dimensionName: '总计',
        participateScore: 0,
        weight: 0,
        questionCount: 0,
        adviseAnswerTime: 0,
    });
    for (let i = 0; i < value.value.versionInfo.length; i++) {
        const reportVersionItem = value.value.versionInfo[i];
        reportVersionItem.reportDimensionField = v
            .filter((i, index) => index !== 0)
            .map((x) => {
                return {
                    code: x.encryptDimensionId,
                    dimName: x.dimensionName,
                    showName: x.dimensionName,
                };
            });
    }
}

async function handleTemplateConfirm(v: TemplateData) {
    const { data } = await _productDimensionList({ encProductId: String(encProductId.value) });
    value.value.dimensionConfigList = v.dimensionConfigList
        .map((x, index) => {
            return {
                encryptDimensionId: x.encryptDimensionId,
                dimensionName: x.dimensionName,
                participateScore: x.participateScore,
                weight: x.weight || 0,
                questionCount: x.questionCount,
                adviseAnswerTime: x.adviseAnswerTime,
                rank: index + 2,
                description: x.description,
                answerConsistent: 0,
            };
        })
        .filter((i) => i.encryptDimensionId !== data.dimList[0].encryptId);
    value.value.dimensionConfigList.unshift({
        encryptDimensionId: data.dimList[0].encryptId,
        dimensionName: data.dimList[0].dimensionName,
        participateScore: 0,
        weight: 0,
        questionCount: data.dimList[0].relatedQuestionNum,
        adviseAnswerTime: data.dimList[0].adviseAnswerTime,
        rank: 1,
        description: data.dimList[0].description,
        answerConsistent: 1,
    });
    value.value.dimensionConfigList.push({
        dimensionName: '总计',
        participateScore: 0,
        weight: 0,
        questionCount: 0,
        adviseAnswerTime: 0,
    });

    for (let i = 0; i < value.value.versionInfo.length; i++) {
        const reportVersionItem = value.value.versionInfo[i];
        reportVersionItem.reportDimensionField = v.dimensionConfigList
            .filter((i) => i.encryptDimensionId !== data.dimList[0].encryptId)
            .map((x) => {
                return {
                    code: x.encryptDimensionId,
                    dimName: x.dimensionName,
                    showName: x.dimensionName,
                };
            });
    }
    value.value.instruction = v.instruction || '';
    value.value.questionOrderType = v.answerConfig!.questionOrderType;
    value.value.optionOrderType = v.answerConfig!.optionOrderType;
    value.value.reviewRuleType = v.answerConfig!.reviewRuleType;
    value.value.commitPaperWhenTimeOut = v.answerConfig!.commitPaperWhenTimeOut;
    value.value.timedRuleType = v.answerConfig!.timedRuleType;
    value.value.nextQuestion = v.answerConfig!.nextQuestion;
}

const { validateReportForm } = useValidateReportForm();
const saveLoading = ref(false);

function getParams() {
    const newData: IPaperSaveType = {
        answerConfig: {
            optionOrderType: value.value.optionOrderType!,
            questionOrderType: value.value.questionOrderType!,
            reviewRuleType: value.value.reviewRuleType!,
            timedRuleType: value.value.timedRuleType!,
            nextQuestion: value.value.nextQuestion!,
        },
        encryptId: pageMode.value === PageModeEnum.EDIT ? value.value.encryptId : '',
        instruction: value.value.instruction,
        paperName: value.value.paperName,
        encProductId: value.value.encProductId,
        encTemplateId: value.value.encTemplateId,
        remark: value.value.remark,
        dimensionConfigList: value.value.dimensionConfigList
            .filter((i) => i.dimensionName !== '总计')
            .map((item) => ({
                encryptDimensionId: item.encryptDimensionId,
                participateScore: item.participateScore || 0,
                rank: item.rank,
                weight: item.weight || 0,
                answerConsistent: item.answerConsistent || 0,
            })),
        versionInfo: value.value.versionInfo.map((reportVersionItem) => {
            return {
                extraParam: reportVersionItem.extraParam,
                encProductId: reportVersionItem.encProductId,
                reportLogo: reportVersionItem.reportLogo || '',
                watermark: reportVersionItem.watermark,
                reportTitle: reportVersionItem.reportTitle || '',
                reportVersionReqs: {
                    encVersionId: reportVersionItem.reportVersionList.encPaperVersionId,
                    reportName: reportVersionItem.reportVersionList.reportName,
                    moduleCodes: reportVersionItem.reportVersionList.moduleCodes,
                },
                reportExaminees: JSON.stringify(
                    reportVersionItem.reportExamineeField.map((x) => {
                        return x.code;
                    })
                ),
                reportModules: JSON.stringify(
                    reportVersionItem.reportDimensionField.map((x) => {
                        return {
                            code: x.code,
                            showName: x.showName,
                        };
                    })
                ),
                reportGroupRefer: JSON.stringify(
                    reportVersionItem.reportGroupReferField.map((x) => {
                        return x.code;
                    })
                ),
            };
        }),
    };

    if (currentProductProperty.value.needReportReferenceOrderType) {
        newData.answerConfig.reportReferenceOrderType = value.value.reportReferenceOrderType;
    }

    if (currentProductProperty.value.needDimensionTable) {
        const { getRenameTableValue } = useRenameTableValue();
        const { validateRenameTableForm } = useValidateRenameTableForm();
        newData.dimensionConfigList = getRenameTableValue();

        const isRenameTableValid = validateRenameTableForm();

        if (!isRenameTableValid) {
            return;
        }
    }

    if (currentProductProperty.value.needDimensionTableV2) {
        const { getRenameTableValue } = useRenameTableValueV2();
        const { validateRenameTableForm } = useValidateRenameTableFormV2();
        newData.dimensionConfigList = getRenameTableValue().map((x: any) => {
            return {
                ...x,
                showName: x.dimensionShowName,
            };
        });

        const isRenameTableValid = validateRenameTableForm();

        if (!isRenameTableValid) {
            return;
        }
    }

    if (currentProductProperty.value.needQuestionMethod) {
        newData.questionMethod = value.value.questionMethod;
    }

    return newData;
}

function updateStateFromTemplate() {
    if (!currentProductProperty.value.relayOnTemplate || pageMode.value !== PageModeEnum.ADD) {
        return;
    }

    const { answerConfig, dimensionConfigList, instruction } = selectTemplateDetail.value;
    value.value.optionOrderType = answerConfig.optionOrderType;
    value.value.questionOrderType = answerConfig.questionOrderType;
    value.value.reviewRuleType = answerConfig.reviewRuleType;
    value.value.timedRuleType = answerConfig.timedRuleType;
    value.value.nextQuestion = answerConfig.nextQuestion;
    value.value.instruction = instruction;
    value.value.reportReferenceOrderType = answerConfig.reportReferenceOrderType;

    if (!currentProductProperty.value.needDimensionTableV2) {
        value.value.dimensionConfigList = dimensionConfigList;
    }
}

async function handleSave() {
    try {
        saveLoading.value = true;
        if (currentProductProperty.value.productPaperStep.length === 1) {
            const { success, issues } = await safeParseAsync(PAPER_2_SCHEMA, value.value);
            if (!success) {
                Toast.danger(issues[0].message);
                return;
            }
        }
        const isReportDataValid = await validateReportForm();

        if (!isReportDataValid) {
            return;
        }
        updateStateFromTemplate();

        const params = getParams();

        if (params) {
            const { code } = await _evaluationPaperSave(params);
            if (code === 0) {
                Toast.success('保存成功');
                setLeaveControl(false);
                $router.go(-1);
            }
        } else {
            Toast.danger('校验失败');
        }
    } finally {
        saveLoading.value = false;
    }
}

// const handleDimensionDelete = (dimension: any) => {
//     if (dimension.dimensionName === '总计') {
//         return;
//     }
//     value.value.dimensionConfigList = value.value.dimensionConfigList.filter((x) => x.dimensionId !== dimension.dimensionId);
// };

async function handleNextStep() {
    const result = await safeParseAsync(PAPER_SCHEMA, value.value);

    // 校验inSorce!==0的权重不能为0
    if (!result.success) {
        Toast.danger(result.issues[0].message);
    } else if (value.value.dimensionConfigList.filter((i, index) => index !== 0 && i.participateScore === 1).length < 3) {
        Toast.danger('参与总分的测评维度数量必须大于等于3');
    } else if (value.value.dimensionConfigList.filter((i, index) => index !== 0 && i.participateScore === 1).some((i) => i.weight === 0)) {
        Toast.danger('权重不能为0');
    } else if (value.value.dimensionConfigList.filter((i, index) => index !== 0 && i.participateScore === 1).some((i) => !i.weight)) {
        Toast.danger('权重不能为空');
    } else if (value.value.dimensionConfigList.filter((i, index) => index !== 0 && i.participateScore === 1).reduce((prev, cur) => add(prev, cur.weight || 0), 0) !== 100) {
        Toast.danger('权重之和必须等于100%');
    } else {
        step.value = 2;
    }
}

function handlePreviousStep() {
    step.value = 1;
}

const columns = ref([
    {
        label: '测评维度',
        field: 'dimensionName',
        minWidth: 110,
    },
    {
        label: '是否参与总分',
        field: 'participateScore',
        minWidth: 90,
    },
    {
        label: '权重',
        field: 'weight',
        minWidth: 110,
    },
    {
        label: '题目数',
        field: 'questionCount',
        minWidth: 80,
    },
    {
        label: '建议作答时间',
        field: 'adviseAnswerTime',
        minWidth: 80,
    },
]);

const pageMode = computed<PageModeEnum>(() => {
    if (encPaperId.value) {
        return PageModeEnum.EDIT;
    } else if (encCopyFromId.value) {
        return PageModeEnum.COPYADD;
    } else {
        return PageModeEnum.ADD;
    }
});
const selectedDimension = computed(() => {
    return value.value.dimensionConfigList
        .filter((i) => i.dimensionName !== '总计')
        .map((x) => ({
            encryptDimensionId: x.encryptDimensionId,
            dimensionName: x.dimensionName,
        }));
});
const pageTitleMap = {
    [PageModeEnum.ADD]: '新增试卷',
    [PageModeEnum.COPYADD]: '新增试卷',
    [PageModeEnum.EDIT]: '编辑试卷',
};
const productName = ref('--');
const plainProductId = ref();
const productList = await useProductList(false, (productList) => {
    plainProductId.value = (productList.find((x) => x.value === encProductId.value) as any)?.productId;
});
const currentProductProperty = computed<IProductProperty>(() => productProperty.find((x) => x.productId === plainProductId.value) ?? ({} as IProductProperty));
setCurrentProductProperty(currentProductProperty.value);
const detailLoading = ref(true);
async function init() {
    await Promise.all([getReportTemplateDetail(), getTemplateList(), getReportModuleList(currentProductProperty.value.productId), getExamineeInfoMap(encProductId.value)]);
    try {
        if (pageMode.value === PageModeEnum.ADD) {
            step.value = currentProductProperty.value?.productPaperStep?.[0] ?? 1;
            if (!currentProductProperty.value.disableGenDefault) {
                await createDefaultReport(value.value, currentProductProperty.value, null);
            }
        } else if (pageMode.value === PageModeEnum.EDIT) {
            await getDetail(encPaperId.value as string);
        } else if (pageMode.value === PageModeEnum.COPYADD) {
            await getDetail(encCopyFromId.value as string);
        } else {
            step.value = currentProductProperty.value?.productPaperStep?.[0] ?? 1;
        }
        findProductName();
    } catch (error) {
        // console.log('%c [ error ]-759', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    } finally {
        detailLoading.value = false;
    }
}
// init();
function findProductName() {
    let productId = '';
    if (pageMode.value === PageModeEnum.ADD) {
        productId = encProductId.value;
    } else if ([PageModeEnum.EDIT, PageModeEnum.COPYADD].includes(pageMode.value)) {
        productId = value.value.encProductId;
    } else {
        productId = encProductId.value;
    }
    productName.value = (productList.find((x) => x.value === productId) as any)?.label ?? '--';
}
async function getDetail(paperId: string) {
    const params = {
        encPaperId: paperId,
    };
    const res = await _evaluationPaperDetail(params);
    if (res.code === 0) {
        SupplementList({ value: res.data.info.encTemplateId, label: res.data.info.templateName });
        await getPaperTemplateDetail(res.data.info.encTemplateId);
        value.value = res.data.info;
        value.value.dimensionConfigList.push({
            dimensionName: '总计',
            participateScore: 0,
            weight: 0,
            questionCount: 0,
            adviseAnswerTime: 0,
        });
        value.value.dimensionConfigList.forEach((i: any) => {
            i.participateScore = i.inScore;
            i.weight = i.weight || 0;
        });
        value.value.versionInfo = value.value.versionInfo.map((x) => {
            return {
                ...x,
                encProductId: x.reportVersionList.encProductId,
                reportVersionList: {
                    ...x.reportVersionList,
                    disabled: x.reportVersionList.linked,
                },
            };
        });
        if (pageMode.value === PageModeEnum.EDIT) {
            isLinkExam.value = res.data.linkExam === 1;
            if (currentProductProperty.value?.productPaperStep?.length === 1) {
                step.value = currentProductProperty.value?.productPaperStep?.[0];
            } else if (res.data.linkExam === 1 && value.value.versionInfo.length !== 3) {
                step.value = 2;
            } else {
                step.value = 1;
            }
        }
        if (pageMode.value === PageModeEnum.COPYADD) {
            value.value.paperName = `${value.value.paperName}_copy`;
        }
    }
}

watch(
    value,
    () => {
        setLeaveControl(true);
    },
    {
        deep: true,
    }
);

onBeforeRouteLeave(async (to, from, next) => {
    if (getLeaveControl()) {
        openLeaveDialog(() => {
            $router.push(to);
        });
        next(false);
    } else {
        next();
    }
});
</script>

<style lang="less">
@import url('./detail-form.less');
@import url('./detail-report.less');
</style>
