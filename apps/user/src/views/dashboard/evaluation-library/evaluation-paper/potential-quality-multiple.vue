<template>
    <div class="compare-job-container">
        <b-select
            :disabled="props.disabled"
            multiple
            :limit="3"
            :modelValue="keyPotentialQualityList?.map((i) => i.name)"
            placeholder="请选择"
            size="large"
            allowSearch
            @exceedLimit="exceedLimit"
            @update:modelValue="handleUpdate"
        >
            <b-option v-for="item of list" :key="item.name" :value="item.name" :label="item.name" />
        </b-select>
        <b-table v-if="keyPotentialQualityList.length > 0" :tableData="keyPotentialQualityList" :columns="columns">
            <template #td-jobShowName="{ raw }">
                <b-input v-model.trim="raw.name" :disabled="props.disabled" size="small" placeholder="请输入" :maxLength="10" trimBeforePaste />
            </template>
            <template #td-priority="{ raw }">
                <b-input-number
                    v-model.trim="raw.priority"
                    :min="0"
                    :precision="0"
                    :max="9999"
                    :disabled="props.disabled"
                    hideButton
                    size="small"
                    placeholder="请输入"
                    :maxLength="10"
                    trimBeforePaste
                />
            </template>
        </b-table>
    </div>
</template>

<script setup lang="ts">
import { _getPaperTemplateConfig } from '@/services/api/evaluation-library';
import { ref, watchEffect } from 'vue';

defineOptions({
    name: 'MatchJobMultiple',
});
interface IKeyPotentialQuality {
    id: string | number;
    name: string;
    showName?: string;
    priority?: number;
}
const keyPotentialQualityList = defineModel<IKeyPotentialQuality[]>({
    default: [],
});
const props = defineProps<{
    productId: string;
    disabled: boolean;
}>();

function exceedLimit() {
    Toast.danger('匹配岗位暂支持至多选取3个');
}
function validate() {
    if (!(keyPotentialQualityList.value.length > 0)) {
        Toast.danger('请选择关键潜在素质');
        return false;
    } else if (keyPotentialQualityList.value.find((x) => x.showName === '')) {
        Toast.danger('请填写关键潜在素质展示名称');
        return false;
    } else if (keyPotentialQualityList.value.find((x) => x.priority === undefined)) {
        Toast.danger('请填写关键潜在素质展示优先级');
        return false;
    } else {
        return true;
    }
}

defineExpose({ validate });

const columns = [
    { label: '关键潜在素质', field: 'name', width: 150 },
    { label: '展示名称', field: 'showName' },
    { label: '展示优先级', field: 'priority' },
];

const list = ref<IKeyPotentialQuality[]>([]);

watchEffect(async () => {
    if (!props.productId) {
        return;
    }
    const res = await _getPaperTemplateConfig({ encryptId: props.productId, key: 'keyPotentialQuality' });
    if (res.code === 0) {
        list.value = res.data || [];
    }
});

function handleUpdate(value: string[]) {
    keyPotentialQualityList.value = list.value
        .filter((i: IKeyPotentialQuality) => value.find((v) => v === i.name))
        .map((job: IKeyPotentialQuality, index: number) => {
            const find = keyPotentialQualityList.value.find((x) => x.name === job.name);
            return {
                id: job.id,
                name: job.name,
                showName: find?.showName || job.name || '',
                priority: find?.priority || index + 1 || undefined,
            };
        });
}
</script>

<style lang="less" scoped>
.compare-job-container {
    width: 100%;
    :deep(.b-table) {
        --b-table-border-radius: 4px;
        --b-table-header-font-size: var(--font-size-title-2);
        --b-table-header-font-color: var(--text-color-7);
        --b-table-header-line-height: 22px;
        --b-table-th-padding: 12px 17px;
        --b-table-body-line-height: 22px;
        --b-table-body-font-color: var(--text-color-7);
        --b-table-td-padding: 12px 17px;
        --input-border-radius: var(--border-radius-small);

        margin-top: 12px;
        margin-bottom: 8px;
        &::before {
            border-bottom-left-radius: var(--b-table-border-radius);
            border-bottom-right-radius: var(--b-table-border-radius);
        }
        .b-table-tr:first-child {
            .b-table-cell::before {
                border-top: 1px var(--b-table-cell-border-color) solid;
            }
        }
        .b-table-tr:last-child {
            .b-table-cell::before {
                display: none;
            }
        }
        .b-table-td {
            height: 46px;
            &:nth-child(2) {
                padding: 9px 28px 9px 17px;
            }
        }
    }
}
</style>
