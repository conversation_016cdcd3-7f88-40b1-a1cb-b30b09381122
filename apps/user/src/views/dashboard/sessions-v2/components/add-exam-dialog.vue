<template>
    <b-drawer
        v-model="visible"
        width="85%"
        :layerClosable="false"
        :footer="false"
        wrapClass="add-exam-dialog-drawer-wrap"
        unmountOnClose
        @confirm="handleConfirm"
        @cancel="handleCancel"
        @close="handleCancel"
    >
        <template #title>
            <div class="header-wrap">
                <div class="title-wrap">
                    <h3>配置{{ isEvaluation ? '测评' : '考试' }}</h3>
                    <div class="divider" />
                    <TextTab v-model="curTab" :data="tabList" />
                </div>
            </div>
        </template>
        <div v-loading="loading" class="content" id="addExamDialog">
            <div v-if="tabList.length" class="component-wrap">
                <component
                    :is="menuMap[curTab]"
                    :isDisableEditing="isDisableEditing"
                    :isShowCombinationType="isShowCombinationType"
                    @save="saveExamInfo"
                    @getExamPrivilegeCodes="getExamPrivilegeCodes"
                />
            </div>
        </div>
    </b-drawer>
</template>

<script setup lang="ts">
import { _examPrivilegeCodes, _examPrivilegeMenu } from '@/services/api/exam';
import { ExamineeStatus, ExamStatus } from '@/services/api/sequence/enums';
import { RelateExamSource } from '@/services/api/continuous-exam';
import { computed, provide, ref } from 'vue';
import { authCodesEnum } from './add-exam-content/constant';
import ExamPersonnel from './add-exam-content/personnerl/index.vue';
import ExamReport from './add-exam-content/report/index.vue';
import ExamSetting from './add-exam-content/setting/index.vue';

const emit = defineEmits(['confirm']);
const visible = ref(false);
const curTab = ref('');
const examCommonDetail = ref<any>({});
const sequenceDetail = ref();
const isShowCombinationType = ref(false);
const currentData = ref<any>({});

const isEvaluation = computed(() => currentData.value?.examSource === RelateExamSource.Evaluation);
const encryptExamId = computed(() => examCommonDetail.value?.encryptId);

provide('examCommonDetail', examCommonDetail);

provide('encryptExamId', encryptExamId);

provide('isEvaluation', isEvaluation);

const isDisableEditing = computed(() => {
    // 场次已发送考生通知、场次进行中和场次已结束状态下不可进行场次编辑
    return (
        [ExamStatus.Ongoing, ExamStatus.Ended].includes(examCommonDetail.value?.status) ||
        ExamineeStatus.Sent === examCommonDetail.value?.examineeStatus ||
        !currentData.value?.editable
    );
});

provide('isDisableEditing', isDisableEditing);

function handleConfirm() {
    visible.value = false;
}
function handleCancel() {
    visible.value = false;
    examCommonDetail.value = {};
    currentData.value = {};
    sequenceDetail.value = {
        templateList: [],
    };
}
const rowIndex = ref(0);
function open(row: any, index: number) {
    visible.value = true;
    curTab.value = '1';
    examCommonDetail.value = {
        ...(row?.examDetail || {}),
        combinationType: row?.combinationType,
        examineeStatus: row?.examineeStatus || row.examDetail?.examineeStatus,
    };
    isShowCombinationType.value = index !== 0;
    rowIndex.value = index;
    currentData.value = row;
    if (isEvaluation.value) {
        examCommonDetail.value = {
            ...(row.sequenceDetail || {}),
            examineeStatus: row?.examineeStatus || row.sequenceDetail?.examineeStatus,
            combinationType: row?.combinationType || row.sequenceDetail?.combinationType,
            templateList: row.sequenceDetail?.templateList || [],
            relateExamList: row.relateExamList || [],
            examineeInfo: row.examineeInfoList || [],
        };
    }

    getExamPrivilegeCodes();
    getExamPrivilegeMenu(isEvaluation.value ? row?.sequenceDetail?.status || 0 : row?.examDetail?.status || 0);
}
const menuMap: any = computed(() => ({
    1: ExamSetting,
    2: ExamPersonnel,
    4: ExamReport,
}));

const tabList = ref<any>([]);
const loading = ref(false);
async function getExamPrivilegeMenu(status: number) {
    try {
        if (!encryptExamId.value) {
            tabList.value = [{ name: '基础设置', value: 1 }];
            return;
        }
        tabList.value = [];
        loading.value = true;
        const { code, data, message } = await _examPrivilegeMenu({ encryptExamId: encryptExamId.value });
        data?.menu?.forEach((item: any) => {
            const option = { ...item, value: authCodesEnum[item.name] };
            if (option.value === authCodesEnum.考试报告 && !isEvaluation.value) {
                // 状态 0 待启用、1 待开始、2 考试中、3 已结束, 考试报告在考试开启后展示
                if (Number(status ?? 0) !== 0) {
                    tabList.value.push(option);
                }
                return;
            }
            if (option.value === authCodesEnum.考试报告 && isEvaluation.value) {
                return;
            }
            tabList.value.push(option);
        });
        if (code !== 0) {
            Toast.danger(message || '获取考试详情权限失败');
        }
    } catch (error: any) {
        Toast.danger(error.message);
    } finally {
        loading.value = false;
    }
}
/**
 * 获取用户按钮的权限
 */
const codeData = ref(new Set([]));
provide('codeData', codeData);
async function getExamPrivilegeCodes() {
    try {
        if (!encryptExamId.value) {
            return;
        }
        const { code, data, message } = await _examPrivilegeCodes({ encryptExamId: encryptExamId.value });
        if (code === 0) {
            codeData.value = new Set(data?.codes || []);
            return;
        }
        Toast.danger(message || '获取考试详情按钮权限失败');
    } catch (e: any) {
        Toast.danger(e.message || '获取考试详情按钮权限失败');
    }
}
async function saveExamInfo(params: any) {
    const examSaveReq = {
        ...params,
        encryptId: examCommonDetail.value?.encryptId,
    };
    emit('confirm', examSaveReq, rowIndex.value);
    handleCancel();
}
defineExpose({
    open,
});
</script>

<style scoped lang="less">
@import '@/styles/exam/add.less';
.exam-add-wrap {
    background-color: var(--base-background-color);
}
.header-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-wrap {
        display: flex;
        align-items: baseline;
        h3 {
            font-size: 18px;
            font-weight: 500;
        }
        .divider {
            width: 1px;
            height: 18px;
            background: #d3d8e6;
            margin: 0 20px;
            transform: translateY(3px);
        }
    }
}
</style>

<style lang="less">
.add-exam-dialog-drawer-wrap {
    .b-drawer-header {
        padding: 34px 32px !important;
        border-bottom: unset !important;
    }
}
</style>
