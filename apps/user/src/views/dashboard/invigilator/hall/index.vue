<template>
    <b-layout v-loading="loading" type="content" direction="vertical" class="hall-wrap">
        <template v-if="!loading && !errorText">
            <b-header :style="{ height: `${80 * scales}px` }" :showUser="isShowInfoTip">
                <template #logo>
                    <a href="javascript:;" class="header-backup use-scale" @click="onGoBackUp">
                        <SvgIcon name="empty-arrow-right" class="icon-backup" width="14" height="14" />返回大屏
                    </a>
                </template>
                <template #nav>
                    <p :style="{ fontSize: `${32 * scales}px` }">【{{ baseInfo.examName || '--' }}】监考详情</p>
                </template>
                <template #user>
                    <InfoTip v-if="isShowInfoTip" :encryptUserId="baseInfo.encryptUserId" />
                </template>
            </b-header>
            <div class="list">
                <!-- 列表筛选条件 -->
                <b-section direction="vertical" type="search" class="box">
                    <!-- 疑似作弊 -->
                    <b-tab v-model="formData.examineeAnswerStatus" @change="onChangeAnswerStatus">
                        <b-tab-panel v-for="item of STATUS_LIST" :key="item.value">
                            <template #title>
                                <div :style="{ fontSize: `${14 * scales}px` }">
                                    {{ item.label }}
                                </div>
                            </template>
                        </b-tab-panel>
                    </b-tab>
                    <!-- 发起公告 -->
                    <b-button v-if="!inspector" type="primary" size="large" class="btn-notice use-scale" @click="onLaunchNotice">
                        <SvgIcon class="icon-notice" name="notice" />发起群公告
                    </b-button>
                    <div class="form-search use-scale">
                        <div class="tab-wrap">
                            <a
                                v-for="item of [{ label: '全部视角', value: 'all' }].concat(tabList)"
                                :key="item.value"
                                href="javascript:;"
                                class="tab-item"
                                :class="[{ isActive: curTab === item.value }]"
                                @click.prevent="onChangeTab(item.value)"
                            >
                                <SvgIcon v-if="item.icon" :name="item.icon" width="14" height="14" />{{ item.label }}
                            </a>
                        </div>
                        <b-form labelAlign="inner" :model="formData" size="large" layout="inline" @submit.prevent="onSearch()">
                            <b-form-item hideLabel>
                                <b-input-search
                                    v-model.trim="formData.keyword"
                                    placeholder="搜索姓名/手机/邮箱/ID"
                                    :maxLength="50"
                                    size="xlarge"
                                    class="keyword"
                                    @pressEnter="onSearch()"
                                    @search="onSearch()"
                                />
                            </b-form-item>
                            <b-form-item v-if="monitoringTypeList.length" label="疑似作弊">
                                <b-select v-model="formData.monitoringType" placeholder="请选择" size="xlarge" :triggerProps="{ class: 'use-scale' }" @change="onSearch()">
                                    <b-option v-for="item in monitoringTypeList" :key="item.value" :label="item.name" :value="item.value" />
                                </b-select>
                            </b-form-item>
                            <b-form-item label="视频异常类型" class="err-type-item">
                                <b-cascader
                                    v-model="formData.errTypeList"
                                    placeholder="请选择"
                                    multiple
                                    :maxTagCount="1"
                                    :options="errTypeOptions"
                                    expandTrigger="hover"
                                    collapseTagsTooltip
                                    size="xlarge"
                                    class="err-cascader"
                                    :triggerProps="triggerProps"
                                    @popupVisibleChange="popupVisibleChange"
                                    @change="onSearch()"
                                />
                            </b-form-item>
                        </b-form>
                    </div>
                </b-section>
                <!-- 监控视频 -->
                <b-layout v-loading="dataLoading" type="content" direction="vertical" class="components-wrap">
                    <DataBlank v-if="!dataSource.length" :empty="listErrorText === '暂无数据'" :error="listErrorText !== '暂无数据'" :errorText="listErrorText" class="use-scale" />
                    <component :is="pageView[curTab]" v-else :key="curTab" />
                </b-layout>
                <!-- 分页 -->
                <b-footer-bar>
                    <b-pagination :page="page" :total="total" :pageSize="pageSize" showTotal class="use-scale" @update:page="onChangePage" />
                </b-footer-bar>
            </div>
        </template>
        <DataBlank v-if="errorText" error :errorText="errorText" class="use-scale" />
        <!--    查看考生详情 -->
        <DetailDialog />
        <!--    发起群公告 -->
        <NoticeDialog v-if="noticeVisible" @close="noticeVisible = false" />
    </b-layout>
</template>

<script setup lang="ts">
import { useScale } from '@/hooks/useScale';
import { useSocketGlobalState } from '@/hooks/useSocketGlobalState';
import { _userCurrent } from '@/services/api/common';
import { RelateExamSource } from '@/services/api/continuous-exam';
import { _invigilatorBaseInfo, _invigilatorMonitoringCount, _invigilatorPrivilegeCodes } from '@/services/api/invigilate';
import { useStore } from '@/store/index';
import { computed, nextTick, onBeforeUnmount, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AllView from './components/all-view.vue';
import DetailDialog from './components/detail-dialog.vue';
import InfoTip from './components/info-tip.vue';
import NoticeDialog from './components/notice-dialog.vue';
import SingleView from './components/single-view.vue';
import { errTypeOptions, INVIGILATE_VISUAL_ANGLE_TAB_LIST, MONITORING_TYPE, STATUS_LIST } from './constant';
import { useSharedHooks } from './useHooks';

const { scales, setUseScale } = useScale();
const { inspector } = useSocketGlobalState();
const {
    getList,
    dataSource,
    roomParams,
    total,
    loadingTimer,
    page,
    formData,
    dataLoading,
    curTab,
    rtc,
    tabList,
    authCodes,
    unsubscribeStream,
    pageSize,
    listErrorText,
    isEvaluation,
} = useSharedHooks();
const $router = useRouter();
const $route = useRoute();
const $store = useStore();

const pageView: any = {
    all: AllView,
    pc: SingleView,
    phone: SingleView,
    answer: SingleView,
};
const encryptId = computed(() => $route?.query?.encryptId || ''); // 考试加密id

const monitoringTypeList = ref<any>([]);

/**
 * 返回监考大屏
 */
function onGoBackUp() {
    $router.push(`/extend/invigilator/screen/${encryptId.value}`);
}
/**
 * 发布公告
 */
const noticeVisible = ref(false);
function onLaunchNotice() {
    noticeVisible.value = true;
}
const triggerProps = ref<any>();
function popupVisibleChange(visible: boolean) {
    if (visible) {
        triggerProps.value = { class: 'use-scale' };
        setUseScale();
    }
}
/**
 * 切换视角（全部视角、电脑摄像头、手机摄像头、答题页面）
 */
function onChangeTab(tab: any) {
    $store.cancelToken.cancelToken('invigilatorRoomList');
    curTab.value = tab;
}
function onChangeAnswerStatus(examineeAnswerStatus: string) {
    formData.examineeAnswerStatus = examineeAnswerStatus;
    onSearch();
}
/**
 * 切换分页
 * @param pageNum
 */
function onChangePage(pageNum: number) {
    $store.cancelToken.cancelToken('invigilatorRoomList');
    page.value = pageNum;
    getList();
}
function onSearch() {
    page.value = 1;
    getList();
}
/**
 * 消息通知权限控制（小铃铛）
 */
const isShowInfoTip = computed(() => {
    const { encryptUserId } = baseInfo.value || {};
    return encryptUserId && authCodes.value.has('INVIGILATOR:BASE_FUNCTION') && !inspector.value;
});
/**
 * 获取考试&考官基本信息
 * encryptId： 考试加密id
 */
const baseInfo = ref<any>({});
const loading = ref<boolean>(false);
const errorText = ref('');
async function getBaseInfo() {
    try {
        const { encryptId } = $route?.query || {};
        if (!encryptId) {
            return;
        }
        const { code, data, message } = await _invigilatorBaseInfo({ encryptId });
        const currentUserInfo = await _userCurrent();
        errorText.value = code !== 0 ? message : '';
        if (errorText.value.includes('您不是本场考试的监考')) {
            setTimeout(() => {
                window.location.replace('/invigilator/list');
            }, 3000);
        }
        if (code === 0) {
            baseInfo.value = data || {};
            isEvaluation.value = data?.examSource === RelateExamSource.Evaluation; // 考试来源 0考试 2测评
            inspector.value = data?.inspector;
            tabList.value = INVIGILATE_VISUAL_ANGLE_TAB_LIST.filter((item) => Number(data[item.code]) === 1);
            const { chatService } = await import('@/services/socket');
            chatService.connect({
                examId: encryptId,
                encryptMobile: baseInfo.value?.encryptMobile,
                userId: baseInfo.value?.encryptUserId,
                wsConnectSecrect: currentUserInfo?.data?.wsConnectSecrect,
            });
            onSearch();
        }
        // 疑似作弊权限控制
        const res = await _invigilatorMonitoringCount({ encryptId });
        monitoringTypeList.value = MONITORING_TYPE.filter((item: any) => String((res?.data || {})[item.key]) !== '-1');

        const { appId, encryptUserId: userId, nebulaId: roomId, signature: userSig, authorization: userAuth } = data || {};
        roomParams.value = {
            appId,
            userId,
            roomId,
            userSig,
            userAuth,
            isSubscribeAllStream: false,
            encryptExamId: $route?.query?.encryptId,
        };
        await nextTick();
        setUseScale();
    } catch (error: any) {
        Toast.danger(error.message);
    } finally {
        loading.value = false;
    }
}
/**
 * 监考-获取用户是否有相关按钮的权限
 * encryptId: 加密考试id
 */
async function getPrivilegeCodes() {
    try {
        const { encryptId } = $route?.query || {};
        if (!encryptId) {
            return;
        }
        loading.value = true;
        const { code, data, message } = await _invigilatorPrivilegeCodes({ encryptId });
        if (code === 0) {
            authCodes.value = new Set(data?.codes || []);
            await getBaseInfo();
            return;
        }
        loading.value = false;
        errorText.value = message;
    } catch (error: any) {
        loading.value = false;
        Toast.danger(error.message);
    }
}
getPrivilegeCodes();

onBeforeUnmount(() => {
    clearTimeout(loadingTimer);
    unsubscribeStream();
    rtc?.leave(() => {});
});
</script>

<style lang="less" scoped>
@import 'style/index';
</style>
