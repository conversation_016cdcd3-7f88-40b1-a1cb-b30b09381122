<template>
    <b-drawer
        v-model="showDetailDialog"
        unmountOnClose
        :footer="false"
        :layerClosable="false"
        wrapClass="invigilator-detail-dialog"
        @confirm="handleConfirm"
        @cancel="handleCancel"
    >
        <template #title>
            <div class="custom-title">
                <div class="left-wrap use-scale">
                    <span>考生详情·{{ currentRow.name }}</span>
                    <span class="phoneLast4Number">
                        手机号：{{ currentRow.phoneLast4Number }} <span class="id"> ID: {{ currentRow.examineeId || '--' }}</span>
                    </span>
                </div>
                <div class="invigilator use-scale">监考人：{{ currentRow.examinerName || '--' }}</div>
            </div>
        </template>
        <b-grid :columns="3">
            <b-grid-item :span="2" class="left-wrap-content">
                <div class="tab-box">
                    <div v-for="tabItem in tabList" :key="tabItem.value" class="detail-tab-item" :class="{ isActive: tabItem.value === curDetailTab }">
                        <span class="video-group" :style="{ height: `${135 * scales}px` }" @click.prevent="onClickVideoTab(tabItem.value)">
                            <span v-if="currentRow.compulsorySubmission" class="exam-status">
                                <span class="use-scale">强制交卷</span>
                            </span>
                            <span v-else-if="currentRow.submitted" class="exam-status">
                                <span class="use-scale">考生已交卷</span>
                            </span>
                            <template v-else>
                                <template v-if="!subscribedSuccessStreamMap[`${tabItem.value}_${encryptExamineeId}`]">
                                    <SystemError v-if="systemError" :rowData="currentRow" :tab="tabItem.value" />
                                    <ErrorActionType v-else :rowData="currentRow" :tab="tabItem.value" />
                                </template>
                                <template v-else>
                                    <VideoPlayError :rowData="currentRow" :tab="tabItem.value" />
                                    <video :id="`video-${tabItem.value}`" height="135" width="240" autoplay muted @play="handlePlay(tabItem)" />
                                    <span v-loading="!tabItem.play && !isBanned" class="loading" />
                                </template>
                            </template>
                        </span>
                        <span class="use-scale">{{ tabItem.label }}</span>
                    </div>
                </div>
                <div
                    class="large-detail-content"
                    :style="{ minHeight: `${350 * scales}px`, height: `${350 * scales}px` }"
                    :class="{ 'large-Video': remoteStreamsMap[`${curDetailTab}_${encryptExamineeId}`] }"
                >
                    <span v-if="currentRow.compulsorySubmission" class="exam-status screen">
                        <span class="invigilate-hall-monitor-compulsory-text use-scale">强制交卷</span>
                    </span>
                    <span v-else-if="currentRow.submitted" class="exam-status screen">
                        <span class="invigilate-hall-monitor-compulsory-text use-scale">考生已交卷</span>
                    </span>
                    <template v-else>
                        <template v-if="!subscribedSuccessStreamMap[`${curDetailTab}_${encryptExamineeId}`]">
                            <SystemError v-if="systemError" :rowData="currentRow" :tab="curDetailTab" />
                            <ErrorActionType v-else :rowData="currentRow" :tab="curDetailTab" />
                        </template>
                        <template v-else>
                            <VideoPlayError :rowData="currentRow" :tab="curDetailTab" />
                            <video
                                :id="`${curDetailTab}-largeVideo`"
                                ref="videoRef"
                                v-loading="!loadingStream[`${curDetailTab}_${encryptExamineeId}`] && !isBanned"
                                style="object-fit: contain; max-width: 740px; height: 100%"
                                autoplay
                                @click="handlerClickVideo"
                            />
                            <audio :id="`${curDetailTab}-largeAudio`" style="display: none" autoplay />
                        </template>
                    </template>
                </div>
            </b-grid-item>
            <b-grid-item class="right-wrap">
                <div class="header">
                    <TextTab v-model="activeName" :data="operateTabList" type="card" class="operate-tab use-scale" :class="{ 'set-min-width': operateTabList.length > 1 }" />
                    <b-dropdown v-if="isShowDropDown" position="bottom" @popupVisibleChange="popupVisibleChange">
                        <b-button type="primary" size="large" class="drop-down use-scale">
                            处理操作
                            <SvgIcon class="operate-arrow-icon" name="hicon-arrow" width="17" height="17" />
                        </b-button>
                        <template #content>
                            <b-dropdown-option v-for="item of statusList" :key="item.value" class="use-scale" @click="handleSelectDropdown(item)">
                                <span :class="`dropdown-item-icon-${item.className}`" />
                                {{ item.label }}
                            </b-dropdown-option>
                        </template>
                    </b-dropdown>
                </div>
                <!-- 人工标记和系统标记tab -->
                <div v-if="activeName === 'history'" class="sub-tab">
                    <b-tab v-model="activeSubName" @change="handleChangeSubTab">
                        <b-tab-panel v-for="item of subHistoryTabList" :key="item.value">
                            <template #title>
                                <div :style="{ fontSize: `${14 * scales}px` }">
                                    {{ item.name }}
                                </div>
                            </template>
                        </b-tab-panel>
                    </b-tab>
                </div>
                <div class="box" :class="{ 'history-box': activeName === 'history' }" :style="{ fontSize: `${14 * scales}px` }">
                    <Component :is="componentPage[activeSubName]" v-if="activeName === 'history'" ref="componentRef" :key="activeSubName" :encryptExamineeId="encryptExamineeId" />
                    <ChatRecord v-else :row="currentRow" :encryptExamineeId="encryptExamineeId" :showFooter="!inspector" />
                </div>
            </b-grid-item>
        </b-grid>
    </b-drawer>
    <OperateDialog v-if="showOperateDialog" :encryptExamineeId="encryptExamineeId" v-bind="dialogParams" @close="handleOperateDialogCancel" @success="updateOperateHistory" />
</template>

<script setup lang="ts">
import HistoryRecord from '@/components/business/history-record/index.vue';
import SystemRecord from '@/components/business/system-record/index.vue';
import { useScale } from '@/hooks/useScale';
import { useSocketGlobalState } from '@/hooks/useSocketGlobalState';
import { useSharedHooks } from '@/views/dashboard/invigilator/hall/useHooks';
import { computed, nextTick, ref, watchEffect } from 'vue';
import { INVIGILATE_DETAIL_DEAL_CHAT_LIST, INVIGILATE_DETAIL_DEAL_LIST } from '../constant';
import ErrorActionType from './error-action-type.vue';
import OperateDialog from './operate-dialog.vue';
import SystemError from './system-error.vue';
import VideoPlayError from './video-play-error.vue';

const { scales, setUseScale } = useScale();
const { inspector } = useSocketGlobalState();

const componentPage: any = {
    artificial: HistoryRecord,
    system: SystemRecord,
};
const subHistoryTabList = [
    { name: '人工标记', value: 'artificial' },
    { name: '系统标记', value: 'system' },
];
const {
    dataSource,
    authCodes,
    showDetailDialog,
    currentRow,
    currentRowIndex,
    tabList,
    curDetailTab,
    subscribedSuccessStreamMap,
    systemError,
    loadingStream,
    isBanned,
    remoteStreamsMap,
    openCommunicateInlet,
    playDetailLargeVideo,
    isEvaluation,
} = useSharedHooks();

const encryptExamineeId = computed(() => currentRow.value.encryptExamineeId);
const statusList = computed(() => {
    const list = INVIGILATE_DETAIL_DEAL_LIST.filter((item: any) => authCodes.value.has(item.code));
    if (isEvaluation.value) {
        return list.filter((item: any) => item.value !== 3);
    }
    return list;
});
const isShowDropDown = computed(() => statusList.value.length && !inspector.value);

const operateTabList = computed(() => {
    return INVIGILATE_DETAIL_DEAL_CHAT_LIST.filter((item: any) => {
        if (item.value === 'chat') {
            // 开启沟通入口：0-否；1-是
            return authCodes.value.has(item.code) && openCommunicateInlet.value;
        }
        return (item.code && authCodes.value.has(item.code)) || !item.code;
    });
});
const activeName = ref<string>('');
const activeSubName = ref<string>('artificial');

function handleChangeSubTab(tab: string) {
    activeSubName.value = tab;
}
function handleConfirm() {
    handleCancel();
}
function handleCancel() {
    showDetailDialog.value = false;
    tabList.value = tabList.value.map((item: any) => ({ ...item, play: false }));
    activeName.value = operateTabList.value[0]?.value || '';
    activeSubName.value = 'artificial';
}
/**
 * 切换三个视角
 * @param value
 */
function onClickVideoTab(value: string) {
    if (value === curDetailTab.value) {
        return;
    }
    curDetailTab.value = value;
    const { compulsorySubmission, submitted } = currentRow.value || {};
    if (compulsorySubmission || submitted) {
        return;
    }
    playDetailLargeVideo();
}
const videoRef = ref<any>();
function handlerClickVideo() {
    if (videoRef.value.requestFullscreen) {
        videoRef.value.requestFullscreen();
    } else if (videoRef.value.mozRequestFullScreen) {
        videoRef.value.mozRequestFullScreen();
    } else if (videoRef.value.webkitRequestFullscreen) {
        videoRef.value.webkitRequestFullscreen();
    } else if (videoRef.value.msRequestFullscreen) {
        videoRef.value.msRequestFullscreen();
    }
}
function handlePlay(row: { play?: boolean }) {
    row.play = true;
}
const showOperateDialog = ref(false);
const dialogParams = ref<any>({
    title: '标记异常',
    confirmText: '确定',
    placeholder: '请输入',
    operateType: 1,
    className: 'abnormal',
});
function handleSelectDropdown(row: any) {
    const { className, label, confirmText, placeholder, value, maxlength } = row;
    showOperateDialog.value = true;
    dialogParams.value = {
        className,
        title: label,
        confirmText,
        placeholder,
        maxlength,
        operateType: value,
    };
}
function handleOperateDialogCancel() {
    showOperateDialog.value = false;
}
/**
 * 处理操作成功后更新操作历史记录
 */
const componentRef = ref<any>();
function updateOperateHistory(operateType: number) {
    const { getExamineeOperateHistoryList } = componentRef.value || {};
    if (typeof getExamineeOperateHistoryList === 'function') {
        getExamineeOperateHistoryList();
    }
    const operateTypeMap = {
        1: 'markException',
        2: 'decisionCheating',
        4: 'sendWarning',
    };
    dataSource.value[currentRowIndex.value] = { ...currentRow.value, [operateTypeMap[operateType]]: true };
}
watchEffect(async () => {
    activeName.value = operateTabList.value[0]?.value || '';
});
async function popupVisibleChange(visible: boolean) {
    if (visible) {
        await nextTick();
        setUseScale();
    }
}
</script>

<style scoped lang="less">
@import '../style/detail-dialog';
</style>
