import { useScale } from '@/hooks/useScale';
import { _invigilatorRoomList } from '@/services/api/invigilate';
import { RtcClient } from '@/utils/tools/media';
import { createSharedComposable } from '@crm/vueuse-pro';
import { computed, nextTick, reactive, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { leaveSeatCount, lookAroundCount, lowerHeadCount, MONITORING_PRO_TYPE, multipleFacesCount, pcHeartbeatStatus, phoneHeartbeatStatus, substituteExamCount } from './constant';

function useHooks() {
    const { setUseScale, scales } = useScale();
    const $route = useRoute();
    const $router = useRouter();
    let rtc: any = null;
    const tabNames = ['pc', 'phone', 'answer'];
    const remoteStreamsMap = ref<any>({});
    const tabList = ref<any>([]);
    const curTab = ref<any>('all');
    const subscribedSuccessStreamMap = ref<any>({});
    const isBanned = ref(false);
    const systemError = ref(false);
    let monitoringTypeTimer: any = null;
    const infoTipCurrentRowData = ref<any>({});
    const isEvaluation = ref(false);

    const normalStyle = computed(() => ({ fontSize: `${14 * scales}px` }));
    /**
     * 移除已经订阅流得列表中取消订阅流的数据
     * @param id
     */
    const removeSubscribedItem = (id: string) => {
        subscribedSuccessStreamMap.value[id] = false;
    };
    /**
     * 考生详情弹窗
     */
    const showDetailDialog = ref<boolean>(false);
    const currentRowIndex = ref<any>();
    const curDetailTab = ref('pc');
    const currentRow = computed(() => {
        if (Object.keys(dataSource.value[currentRowIndex.value] || {}).length) {
            return dataSource.value[currentRowIndex.value];
        } else if (Object.keys(infoTipCurrentRowData.value || {}).length) {
            return infoTipCurrentRowData.value;
        } else {
            return {};
        }
    });
    async function playDetailVideo() {
        await nextTick();
        tabNames.forEach((tabItem) => {
            const dom: any = document.getElementById(`video-${tabItem}`);
            if (dom) {
                dom.srcObject = remoteStreamsMap.value[`${tabItem}_${currentRow.value.encryptExamineeId}`].stream.videoStream;
            }
        });
        playDetailLargeVideo();
    }
    /**
     * 考生详情:播放大屏音视频流
     */
    async function playDetailLargeVideo() {
        await nextTick();
        const remoteStream: any = remoteStreamsMap.value[`${curDetailTab.value}_${currentRow.value.encryptExamineeId}`];
        const dom: any = document.getElementById(`${curDetailTab.value}-largeVideo`);
        const audioDom: any = document.getElementById(`${curDetailTab.value}-largeAudio`);
        if (dom) {
            dom.srcObject = remoteStream?.stream?.videoStream;
        }
        if (audioDom) {
            audioDom.srcObject = remoteStream?.stream?.audioStream;
        }
    }
    /**
     * 考生详情开关对应视频的订阅和取消处理逻辑
     * @param value
     */
    function streamProcess(value: boolean) {
        if (curTab.value !== 'all') {
            tabNames
                .filter((tab) => tab !== curTab.value)
                .forEach((item) => {
                    const userId = `${item}_${currentRow.value.encryptExamineeId}`;
                    const obj = remoteStreamsMap.value[userId];

                    if (!subscribedSuccessStreamMap.value[`${item}_${currentRow.value.encryptExamineeId}`] && value) {
                        rtc?.regainSubscribe(obj);
                    }
                    if (subscribedSuccessStreamMap.value[userId] && !value) {
                        obj?.stream?.stop();
                        rtc?.unsubscribe(obj);
                        removeSubscribedItem(userId);
                    }
                });
        }
    }
    watch(
        () => showDetailDialog.value,
        async (value, oldValue) => {
            if (oldValue !== value) {
                if (value) {
                    playDetailVideo();
                    await nextTick();
                    setUseScale();
                }
                streamProcess(value);
            }
        }
    );
    const formData = reactive({
        examineeAnswerStatus: '-1',
        keyword: '',
        monitoringType: 0,
        errTypeList: [],
    });
    /**
     * 列表
     */
    const dataSource = ref<any>([]);
    /**
     * 切换数据订阅视频流并播放监控视频
     */
    const updateList = () => {
        dataSource.value.forEach((a: any) => {
            if (curTab.value === 'all') {
                tabNames.forEach((tab) => {
                    const obj = remoteStreamsMap.value[`${tab}_${a.encryptExamineeId}`];
                    if (obj && !a.submitted && !a.compulsorySubmission) {
                        if (!subscribedSuccessStreamMap.value[`${tab}_${a.encryptExamineeId}`]) {
                            rtc?.regainSubscribe(obj);
                        }
                    }
                });
            } else {
                const obj = remoteStreamsMap.value[`${curTab.value}_${a.encryptExamineeId}`];
                if (obj && !a.submitted && !a.compulsorySubmission) {
                    if (!subscribedSuccessStreamMap.value[`${curTab.value}_${a.encryptExamineeId}`]) {
                        rtc?.regainSubscribe(obj);
                    }
                }
            }
        });
    };

    const total = ref(0);
    const page = ref(1);
    const pageSize = computed(() => (curTab.value === 'all' ? 6 : 18));
    const dataLoading = ref(false);
    const roomParams = ref<any>({});
    const loadingStream = ref<any>({});
    const openCommunicateInlet = ref('');
    let loadingTimer: any = null;
    const listErrorText = ref<string>('暂无数据');

    async function getList() {
        unsubscribeStream();
        const params = {
            ...formData,
            page: page.value,
            pageSize: pageSize.value,
            encryptId: $route?.query?.encryptId,
            examineeAnswerStatus: String(formData?.examineeAnswerStatus) === '-1' ? '' : formData?.examineeAnswerStatus,
            errTypeList: formData?.errTypeList?.join(','),
            allView: curTab.value === 'all',
        };
        loadingStream.value = {};
        if (loadingTimer) {
            clearTimeout(loadingTimer);
        }
        try {
            dataLoading.value = true;
            const { code, data, message } = await _invigilatorRoomList(params, {
                cancelToken: 'invigilatorRoomList',
            });
            listErrorText.value = code !== 0 ? message : '暂无数据';
            if (listErrorText.value.includes('您不是本场考试的监考')) {
                setTimeout(() => {
                    window.location.replace('/invigilator/list');
                }, 3000);
            }
            openCommunicateInlet.value = data.openCommunicateInlet;
            dataSource.value =
                data?.items?.map((item: any) => ({
                    ...item,
                    pcLoading: true,
                    phoneLoading: true,
                    answerLoading: true,
                    monitoringType: curTab.value === 'all' ? null : item.monitoringType,
                })) || [];
            total.value = data?.total || 0;
            if (code !== 0) {
                Toast.danger(message);
            }

            loadingTimer = setTimeout(() => {
                dataSource.value =
                    dataSource.value?.map((item: any) => ({
                        ...item,
                        pcLoading: false,
                        phoneLoading: false,
                        answerLoading: false,
                    })) || [];
            }, 1000 * 15);

            if (rtc) {
                updateList();
            }

            if (!rtc && roomParams.value.appId && dataSource.value.length) {
                initRtc(roomParams.value);
            }
            await nextTick();
            setUseScale();
        } catch (e: any) {
            if (e.message !== 'canceled') {
                Toast.danger(e.message);
            }
        } finally {
            dataLoading.value = false;
        }
    }
    /**
     * 列表页：播放监控视频画面
     * @param item
     */
    const playVideo = async (item: any) => {
        await nextTick();
        if (document.getElementById(item?.userId)) {
            item?.stream?.play(item?.userId, { objectFit: 'contain', audio: false, video: true, muted: true });
            item?.stream?.on('player-state-changed', (event: { type?: string; state?: string }) => {
                if (event.type === 'video' && event.state === 'STOPPED') {
                    loadingStream.value[item?.userId] = false;
                    item?.stream.resume();
                }
                if (event.type === 'video' && event.state === 'PLAYING') {
                    loadingStream.value[item?.userId] = true;
                    systemError.value = false;
                }
            });
        }
    };
    function onError(params: { errorCode?: number }) {
        if (params.errorCode === 211004) {
            Toast.danger('接口请求超时,请刷新页面！');
        }
        if (params.errorCode !== 211005) {
            systemError.value = true;
        }
    }
    async function onClientBanned() {
        isBanned.value = true;
        remoteStreamsMap.value = {};
        subscribedSuccessStreamMap.value = {};
        clearTimeout(loadingTimer);
        dataSource.value =
            dataSource.value?.map((item: any) => ({
                ...item,
                pcLoading: false,
                phoneLoading: false,
                answerLoading: false,
            })) || [];
        Dialog.open({
            type: 'warning',
            title: '提示',
            footer: false,
            width: 350,
            content: () => '您已在其他地方打开该页面',
        });
    }
    function onStreamRemoved(params: { userId?: any; stream?: any }) {
        remoteStreamsMap.value[params?.userId] = null;
        subscribedSuccessStreamMap.value[params?.userId] = false;
        params?.stream?.stop();
        rtc?.unsubscribe(params);
        removeSubscribedItem(params?.userId);
    }
    function onStreamAdded(params: { userId?: any }, remoteStreamMap?: any) {
        remoteStreamsMap.value = remoteStreamMap || {};
        dataSource.value.forEach((item: any) => {
            if (item.submitted || item.compulsorySubmission) {
                return;
            }
            if (curTab.value === 'all') {
                tabNames.forEach((tab) => {
                    if (params?.userId?.includes(`${tab}_${item.encryptExamineeId}`)) {
                        rtc?.regainSubscribe(params);
                    }
                });
                return;
            }
            if (showDetailDialog.value && params?.userId?.includes(item.encryptExamineeId)) {
                rtc?.regainSubscribe(params);
                return;
            }
            if (params?.userId?.includes(`${curTab.value}_${item.encryptExamineeId}`)) {
                rtc?.regainSubscribe(params);
            }
        });
    }
    function onStreamSubscribed(params: { userId?: any }) {
        subscribedSuccessStreamMap.value[params?.userId] = true;
        playVideo(params);
        const [tabKey, encryptExamineeId] = params?.userId?.split('_') || [];
        const index = dataSource.value?.findIndex((x: any) => x.encryptExamineeId === encryptExamineeId);
        if (index >= 0) {
            dataSource.value[index] = { ...dataSource.value[index], [`${tabKey}Loading`]: false };
        }
        if (showDetailDialog.value) {
            playDetailVideo();
        }
    }
    /**
     * web rtc回调函数
     * @param type
     * @param params
     * @param remoteStreamMap
     */
    function rtcCallBack(type: string, params: { remoteStream: any; userId?: any; errorCode?: number; stream?: any }, remoteStreamMap?: any) {
        if (type === 'error') {
            onError(params);
        }
        if (type === 'client-banned') {
            onClientBanned();
        }
        if (type === 'stream-removed') {
            onStreamRemoved(params);
        }
        if (type === 'stream-updated') {
            // 打开详情页流更新时重新播放（音频流和视频流分开回调）
            if (params?.userId?.includes(currentRow.value.encryptExamineeId)) {
                if (showDetailDialog.value) {
                    playDetailVideo();
                }
            }
        }
        if (type === 'stream-subscribed') {
            onStreamSubscribed(params);
        }
        if (type === 'stream-added') {
            onStreamAdded(params, remoteStreamMap);
        }
    }
    /**
     * 初始化web rtc配置
     */
    function initRtc(roomParams: object) {
        rtc = new RtcClient(roomParams, rtcCallBack);
        rtc.join(() => {});
    }
    function unsubscribeStream() {
        Object.keys(subscribedSuccessStreamMap.value).forEach((item) => {
            const obj = remoteStreamsMap.value[item];
            obj?.stream?.stop();
            rtc?.unsubscribe(obj);
            removeSubscribedItem(item);
        });
    }

    /**
     * ws推送摄像头错误
     * @param params
     */
    function chatPacketSendMonitoringError(params: any) {
        const { encExamineeId, dataSource: source, errType } = params;
        const index = dataSource.value?.findIndex((item: any) => item?.encryptExamineeId === encExamineeId);
        const optionItem = dataSource.value?.find((item: any) => item?.encryptExamineeId === encExamineeId) || {};
        const errActionTypeKeyMap: any = {
            1: 'errActionTypeWeb',
            2: 'errActionTypePhone',
            3: 'errActionTypeScreen',
        };
        if (source === 2) {
            optionItem.phoneLoading = false;
        }
        if (source === 1) {
            optionItem.pcLoading = false;
        }
        if (source === 3) {
            optionItem.answerLoading = false;
        }
        dataSource.value[index] = { ...optionItem, [errActionTypeKeyMap[source]]: errType };
    }

    /**
     * ws活体检测监控类型推送
     * @param params
     */
    function chatPacketSendMonitoringType(res: any) {
        const { from, examId, content, proType } = res;
        if (examId === $route?.query?.encryptId) {
            const index = dataSource.value?.findIndex((item: any) => item?.encryptExamineeId === from?.userId);
            const optionItem = dataSource.value?.find((item: any) => item?.encryptExamineeId === from?.userId) || {};
            if ([pcHeartbeatStatus, phoneHeartbeatStatus].includes(proType) && index >= 0) {
                // 考生心跳（是否离开考试1：正常2：断开）
                const heartbeatBreakType: any = new Set(optionItem.heartbeatBreakType || []);
                content?.heartbeatStatus === 2 ? heartbeatBreakType.add(proType) : heartbeatBreakType.delete(proType);
                dataSource.value[index] = { ...optionItem, heartbeatBreakType: [...(heartbeatBreakType || [])] };
                return;
            }
            if (curTab.value === 'all') {
                // ProType: 23 防作弊检测类型提醒   24 试题完成度   25 考生状态
                if ([23, 24, 25].includes(proType) && index >= 0) {
                    if (monitoringTypeTimer) {
                        clearTimeout(monitoringTypeTimer);
                    }
                    const item = optionItem;
                    if (proType === 23) {
                        item.allViewInfo = {
                            ...(item.allViewInfo || {}),
                            monitoringExceptionTypes: item.allViewInfo?.monitoringExceptionTypes?.some(
                                (x: { exceptionType: number }) => x.exceptionType === res.content?.exceptionType
                            )
                                ? item.allViewInfo?.monitoringExceptionTypes?.map((x: any) => {
                                      if (x.exceptionType === res.content?.exceptionType) {
                                          return res.content;
                                      }
                                      return x;
                                  })
                                : (item.allViewInfo?.monitoringExceptionTypes || []).concat([res.content]),
                        };
                        if (res.content?.exceptionType) {
                            item.monitoringType = res.content?.exceptionType;
                            monitoringTypeTimer = setTimeout(() => {
                                dataSource.value[index] = { ...optionItem, monitoringType: 0 };
                            }, 5 * 1000);
                        }
                    }
                    if (res.proType === 24) {
                        item.allViewInfo = {
                            ...(item.allViewInfo || {}),
                            questionAnswerComplete: res.content?.questionAnswerComplete,
                        };
                    }
                    if (res.proType === 25) {
                        item.allViewInfo = {
                            ...(item.allViewInfo || {}),
                            examineeStatus: res.content?.examineeStatus,
                        };
                    }
                    dataSource.value[index] = item;
                }
                return;
            }
            const monitoringType = MONITORING_PRO_TYPE[res.proType];
            // 出现多个时优先级高进行展示：替考(5)>多张人脸(4)>离开(1)>低头(2)>左右张望(3)
            const priority = [substituteExamCount, multipleFacesCount, leaveSeatCount, lowerHeadCount, lookAroundCount];
            const priorityIndex = priority.indexOf(monitoringType);
            if (priority.slice(0, priorityIndex)?.includes(optionItem.monitoringType)) {
                return;
            }
            if (index >= 0 && Number(formData.monitoringType) === 0) {
                dataSource.value[index] = { ...optionItem, monitoringType };
            }
        }
    }

    /**
     * ws推送考生消息交卷
     * @param params
     */
    function chatPacketSendExamStatus(res: any) {
        if (res?.examId === $route?.query?.encryptId) {
            const index = dataSource.value?.findIndex((item: any) => item?.encryptExamineeId === res?.from?.userId);
            const optionItem = dataSource.value?.find((item: any) => item?.encryptExamineeId === res?.from?.userId) || {};
            if (index >= 0) {
                dataSource.value[index] = { ...optionItem, submitted: true };
            }
        }
    }
    const authCodes = ref(new Set());
    return {
        getList,
        authCodes,
        dataSource,
        normalStyle,
        total,
        roomParams,
        loadingTimer,
        page,
        pageSize,
        formData,
        dataLoading,
        curTab,
        systemError,
        isBanned,
        loadingStream,
        subscribedSuccessStreamMap,
        tabNames,
        remoteStreamsMap,
        rtc,
        showDetailDialog,
        currentRow,
        currentRowIndex,
        tabList,
        curDetailTab,
        openCommunicateInlet,
        infoTipCurrentRowData,
        playDetailLargeVideo,
        removeSubscribedItem,
        unsubscribeStream,
        chatPacketSendMonitoringError,
        chatPacketSendMonitoringType,
        chatPacketSendExamStatus,
        listErrorText,
        isEvaluation,
    };
}
export const useSharedHooks = createSharedComposable(useHooks);
