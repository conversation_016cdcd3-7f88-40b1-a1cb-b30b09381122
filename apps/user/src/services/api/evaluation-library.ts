import type { IData, IPaperSaveType, TemplateData, TemplateListData } from './type';
import { get, post } from '../http';

/**
 * https://api.weizhipin.com/project/2353/interface/api/645552
 * 测评库-创建测评试卷
 */
export function _evaluationPaperSave(params?: IPaperSaveType) {
    return post<Record<string, never>>('/wapi/cadmin/evaluation/paper/save.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/645566
 * 测评库-测评试卷列表
 */
export function _evaluationPaperList(params?: any) {
    return get('/wapi/cadmin/evaluation/paper/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/645594
 * 测评库-测评试卷详情
 */
export function _evaluationPaperDetail(params?: { encPaperId: string }) {
    return get<IData>('/wapi/cadmin/evaluation/paper/detail.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647022
 * 测评库-产品维度列表
 */
export function _productDimensionList(params?: any) {
    return get('/wapi/cadmin/common/dimension/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647036
 * 测评库-考生信息模块
 */
export function _examineeFieldList(params: { encProductId: string }) {
    return get<{
        examineeField: {
            reportType: string;
            code: string;
            name: string;
            checked: '0' | '1';
            require: '0' | '1';
            canChanged: '0' | '1';
        }[];
    }>('/wapi/cadmin/common/examineeField/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647043
 * 测评库-报告版本模块列表
 */
export function _reportModuleList(params?: any) {
    return get('/wapi/cadmin/common/reportVersionModule/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647288
 * 测评库-公共数据-维度互斥提示
 */
export function _getConflictDimensionTips(params?: any) {
    return get('/wapi/cadmin/common/getConflictDimensionTips.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647295
 * 测评库-公共数据-群体参照指标
 */
export function _reportTemplateGroupRefer(params?: { encProductId: string }) {
    return get<{
        groupRefer: {
            id: number;
            createTime: number;
            updateTime: number;
            templateId: number;
            referenceName: string;
            createUserId: number;
            updateUserId: number;
        }[];
    }>('/wapi/cadmin/common/reportTemplateGroupRefer.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647302
 * 测评库-试卷状态修改
 */
export function _updatePaperStatus(params?: any) {
    return get('/wapi/cadmin/evaluation/paper/updateStatus.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647309
 * 测评库-公共数据-模板列表
 */
export function _paperTemplateList(params?: { templateName?: string; encProductId: string; templateStatus?: number; page?: number; pageSize?: number }) {
    return get<{ list: TemplateListData[] }>('/wapi/cadmin/evaluation/paper_template/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647316
 * 测评库-公共数据-模板详情
 */
export function _paperTemplateInfo(params?: { encryptId: string }) {
    return get<TemplateData>('/wapi/cadmin/evaluation/paper_template/info.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/647351
 * 测评库-产品列表
 */
export function _productList(params?: any) {
    return get('/wapi/cadmin/evaluation/product/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/654771
 * 测评库-测评试卷报告预览_动态
 */
export function _reportPreview(params?: any) {
    return post('/wapi/cadmin/evaluation/report/preview.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/655408
 * 测评库-测评试卷预览key
 */
export function _genPreviewKey(params?: any) {
    return post('/wapi/cadmin/evaluation/paper/genPreviewKey.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/682827
 * 测评库-测评试卷报告预览-心里测评_动态
 */
export function _reportPreviewByProductId(params?: any, productId?: number) {
    return post(`/wapi/cadmin/evaluation/report/${productId}/preview.json`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/785165
 * 测评库-公共数据（启用职位、关键潜在素质）
 */
export function _getPaperTemplateConfig(params?: any) {
    return get(`https://api.weizhipin.com/mock/2353/wapi/cadmin/evaluation/paper/config`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/714739
 * 测评报告-查询模板信息
 */
export function _getReportTemplateDetail(params?: any) {
    return get(`/wapi/cadmin/evaluation/report/template/info.json`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/728068
 * 测评报告-模板维度配置
 */
export function _getPaperTemplateDimension(params?: any) {
    return get(`/wapi/cadmin/common/template/dimension/list.json`, params);
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/778804
 * 获取维度接口-政治素养测评专用
 */
export function _getTemplateDimensionList(params?: { encTemplateId: string }) {
    return get(`/wapi/cadmin/common/template/dimension/hierarchical/list.json`, params);
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/778804
 * 获取某个产品的测评维度树
 */
export function _productDimensionTree(params?: { encProductId: string; encTemplateId?: string }) {
    return get('/wapi/cadmin/common/dimension/tree', params);
}

/** 方案组合接口 */

/**
 * https://api.weizhipin.com/project/2353/interface/api/760156
 * 方案组合试卷分页列表
 */
export function _getCombinationPaperList(params?: any) {
    return get(`/wapi/cadmin/solutionCombination/pageList`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/760204
 * 方案组合试卷保存
 */
export function _saveCombinationPaper(params?: any) {
    return post(`/wapi/cadmin/solutionCombination/save`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/760237
 * 方案组合试卷详情
 */
export function _getCombinationPaperDetail(params?: any) {
    return get(`/wapi/cadmin/solutionCombination/detail`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/760246
 * 方案组合试卷类型列表
 */
export function _getCombinationProductList(params?: any) {
    return get(`/wapi/cadmin/solutionCombination/productList`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/760315
 * 方案组合试卷修改状态
 */
export function _changeCombinationPaperStatus(params?: any) {
    return get(`/wapi/cadmin/solutionCombination/updateStatus`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/760816
 * 方案组合试卷可选列表
 */
export function _getCombinationPaperSelectList(params?: any) {
    return get(`/wapi/cadmin/solutionCombination/selectList`, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/761872
 * 测评库-根据测评产品获取测评试卷列表
 */
export function _getPaperList(params?: any) {
    return get(`/wapi/cadmin/evaluation/paper/listByProductId`, params);
}
