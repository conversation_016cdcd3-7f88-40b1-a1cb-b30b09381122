<template>
    <b-layout type="content">
        <b-layout direction="vertical">
            <TableList ref="listTableRef" title="导出任务列表" :getData="ResultService.getExportTaskList" :columns="columns">
                <template #table-title>
                    <b-button type="primary" @click="refreshTaskList"><IconRefresh></IconRefresh> 刷新 </b-button>
                </template>
                <template #td-operation="{ raw }">
                    <b-action @click="resend(raw)">重新执行</b-action>
                </template>
            </TableList>
        </b-layout>
    </b-layout>
</template>

<script setup lang="ts">
import type { ITableColumnDataRaw, ITablePagination } from '@boss/design';
import { IconRefresh } from '@boss/design/es/icon';
import ResultService from '@/services/api/result';
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';
const listTableRef = ref();

const columns: ITableColumnDataRaw[] = [
    {
        label: '任务名称',
        field: 'taskName',
    },
    {
        label: '任务状态',
        field: 'taskStatusStr',
    },
    {
        label: '邮箱',
        field: 'userEmail',
    },
    {
        label: '时间',
        field: 'createTime',
    },
    {
        label: '操作',
        field: 'operation',
    },
];

async function resend(raw: { id: string | number }) {
    try {
        const { code, message } = await ResultService.resendExportTask({ id: raw.id });
        if (code === 0) {
            Toast.success('执行成功');
        } else {
            Toast.danger(message || '重新执行失败');
        }
    } catch (error) {
        Toast.danger(error?.message || '重新执行失败');
    }
}

function refreshTaskList() {
    listTableRef.value.getTableList();
}

onMounted(() => {
    refreshTaskList();
});
</script>
