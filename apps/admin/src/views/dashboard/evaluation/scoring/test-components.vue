<template>
    <div class="test-components">
        <h2>五维性格测评2.0配置组件测试</h2>
        
        <div class="test-section">
            <h3>关键潜在素质配置</h3>
            <KeyPotentialQualityConfig v-model="keyPotentialQualityData" />
        </div>

        <div class="test-section">
            <h3>岗位素质模型配置</h3>
            <JobQualityModelConfig v-model="jobQualityModelData" />
        </div>

        <div class="test-section">
            <h3>团队角色配置</h3>
            <TeamRoleConfig v-model="teamRoleData" />
        </div>

        <div class="test-section">
            <h3>数据输出</h3>
            <pre>{{ JSON.stringify({ keyPotentialQualityData, jobQualityModelData, teamRoleData }, null, 2) }}</pre>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import KeyPotentialQualityConfig from './components/key-potential-quality-config.vue';
import JobQualityModelConfig from './components/job-quality-model-config.vue';
import TeamRoleConfig from './components/team-role-config.vue';

defineOptions({
    name: 'TestComponents',
});

// 测试数据
const keyPotentialQualityData = ref([]);
const jobQualityModelData = ref([]);
const teamRoleData = ref([]);
</script>

<style lang="less" scoped>
.test-components {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.test-section {
    margin-bottom: 40px;
    border: 1px solid #e5e6eb;
    border-radius: 6px;
    padding: 20px;
}

h2 {
    color: #1d2129;
    margin-bottom: 30px;
}

h3 {
    color: #4e5969;
    margin-bottom: 20px;
    border-bottom: 1px solid #e5e6eb;
    padding-bottom: 10px;
}

pre {
    background: #f7f8fa;
    padding: 15px;
    border-radius: 4px;
    overflow: auto;
    max-height: 300px;
    font-size: 12px;
}
</style>
