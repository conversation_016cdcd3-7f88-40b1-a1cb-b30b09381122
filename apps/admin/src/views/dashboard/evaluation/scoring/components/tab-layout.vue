<template>
    <div class="tab-layout-wrap">
        <b-tab v-model="currentTab" type="card-gutter" @tabClick="handleClickTab">
            <b-tab-panel v-for="tabItem in tabList" :key="tabItem.value" :title="tabItem.label" />
        </b-tab>
        <div class="tab-box">
            <div class="content" :class="{ 'score-config': !($slots.footer || showFooter) }">
                <slot />
            </div>
            <div v-if="$slots.footer || showFooter" class="footer">
                <slot name="footer">
                    <b-button type="outline" @click="$emit('cancel')"> 取消 </b-button>
                    <b-button :loading="loading" type="primary" @click="$emit('save')"> 保存 </b-button>
                </slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineOptions({
    name: 'TabLayout',
});
const props = defineProps({
    tabList: {
        type: Array,
        default: () => [
            {
                label: '参数设置',
                value: 1,
            },
            {
                label: '计分规则导入',
                value: 2,
            },
        ],
    },
    modelValue: {
        type: [String, Number],
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
    showFooter: {
        type: Boolean,
        default: false,
    },
});
const $emit = defineEmits(['update:modelValue', 'cancel', 'save']);
const currentTab = ref(props.modelValue);

function handleClickTab(tab: any) {
    currentTab.value = tab;
    $emit('update:modelValue', tab);
}
</script>

<style lang="less" scoped>
.tab-layout-wrap {
    margin-top: 20px;
    :deep(.b-tab-content) {
        display: none;
    }
    .tab-box {
        min-height: calc(~'100vh - 207px');
        padding: 20px 20px 0;
        border: 1px solid #e5e6eb;
        border-top: none;
        margin-bottom: 20px;
        .content {
            width: 100%;
            height: calc(~'100vh - 300px');
            overflow-y: auto;
            overflow-x: hidden;
            display: inline-block;
        }
        .score-config {
            height: calc(~'100vh - 248px');
        }
        .footer {
            background: #fff;
            text-align: center;
            padding: 20px 0;
            position: sticky;
            bottom: 0;
            .b-button + .b-button {
                margin-left: 12px;
            }
        }
    }
}
</style>
