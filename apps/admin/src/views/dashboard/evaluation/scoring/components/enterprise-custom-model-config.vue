<template>
    <div class="enterprise-custom-model-config">
        <div class="sub-title">
            <h4>企业定制模型</h4>
            <b-button type="primary" @click="showAddModelDialog = true">
                <template #icon>
                    <b-icon name="plus" />
                </template>
                新建模型
            </b-button>
        </div>

        <!-- 模型列表 -->
        <b-table v-model:tableData="modelList" :columns="modelColumns" border tableLayoutFixed :scroll="{ y: '400px' }">
            <template #td-corpName="{ raw }">
                {{ raw.corpName || '-' }}
            </template>

            <template #td-name="{ raw }">
                {{ raw.name || '-' }}
            </template>

            <template #td-status="{ raw }">
                <b-tag :color="raw.status === 0 ? 'green' : 'red'">
                    {{ raw.status === 0 ? '启用' : '停用' }}
                </b-tag>
            </template>

            <template #td-operation="{ raw, $index }">
                <b-space>
                    <b-action @click="editModel(raw, $index)">编辑</b-action>
                    <b-action @click="toggleModelStatus(raw, $index)">
                        {{ raw.status === 0 ? '停用' : '启用' }}
                    </b-action>
                    <b-action @click="deleteModel($index)">删除</b-action>
                </b-space>
            </template>
        </b-table>

        <!-- 新建/编辑模型弹窗 -->
        <b-drawer v-model="showAddModelDialog" :title="editingModelIndex === -1 ? '新建模型' : '编辑模型'" width="800px" @confirm="saveModel" @cancel="cancelEditModel">
            <b-form ref="modelFormRef" :model="modelForm" labelAlign="left" :rowNum="1">
                <b-form-item label="所属项目" field="encCorpId" asteriskPosition="end" :rules="[{ required: true, message: '请选择所属项目' }]">
                    <b-select v-model="modelForm.encCorpId" placeholder="请选择项目" @change="onProjectChange">
                        <b-option v-for="project in availableProjects" :key="project.encryptId" :value="project.encryptId" :label="project.name" />
                    </b-select>
                </b-form-item>

                <b-form-item
                    label="模型名称"
                    field="name"
                    asteriskPosition="end"
                    :rules="[
                        { required: true, message: '请输入模型名称' },
                        { max: 50, message: '模型名称不能超过50个字符' },
                    ]"
                >
                    <b-input v-model="modelForm.name" placeholder="请输入模型名称，上限50字" />
                </b-form-item>
            </b-form>

            <!-- 关键潜在素质配置 -->
            <div class="model-config-section">
                <h5>关键潜在素质配置</h5>
                <KeyPotentialQualityTableConfig v-model="modelForm.keyPotentialQualityList" />
            </div>

            <!-- 岗位素质模型配置 -->
            <div class="model-config-section">
                <h5>岗位素质模型配置</h5>
                <PositionQualityModelTableConfig v-model="modelForm.positionQualityModelMatchList" />
            </div>
        </b-drawer>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { _getCorpModelList, _updateCorpModelStatus, _getCorpModelDetail, _saveCorpModel, _getProjectOptions } from '@/services/api/scoring';
import type { ProjectOption, KeyPotentialQualityItem, PositionQualityModelMatchItem } from '../types/enterprise-model';
import KeyPotentialQualityTableConfig from './key-potential-quality-table-config.vue';
import PositionQualityModelTableConfig from './position-quality-model-table-config.vue';

// 全局变量声明
declare const Toast: {
    success: (message: string) => void;
    danger: (message: string) => void;
};

declare const Dialog: {
    open: (options: { type: string; title: string; content: string; confirm: () => void; cancel?: () => void }) => void;
};

interface ModelItem {
    encId?: string;
    encCorpId?: string;
    corpName: string;
    name: string;
    status: number; // 0: 启用, 1: 停用
    keyPotentialQualityList?: KeyPotentialQualityItem[];
    positionQualityModelMatchList?: PositionQualityModelMatchItem[];
}

// 模型列表
const modelList = ref<ModelItem[]>([]);

// 可用项目列表（排除已关联定制模型的项目）
const availableProjects = ref<ProjectOption[]>([]);

// 弹窗控制
const showAddModelDialog = ref(false);
const editingModelIndex = ref(-1);

// 模型表单
const modelForm = ref<ModelItem>({
    corpName: '',
    name: '',
    status: 0,
    keyPotentialQualityList: [],
    positionQualityModelMatchList: [],
});

const modelFormRef = ref();

// 表格列配置
const modelColumns = [
    {
        label: '所属项目',
        field: 'corpName',
        width: 200,
    },
    {
        label: '模型名称',
        field: 'name',
        width: 200,
    },
    {
        label: '状态',
        field: 'status',
        width: 100,
    },
    {
        label: '操作',
        field: 'operation',
        width: 200,
    },
];

// 项目选择变化
function onProjectChange(encCorpId: string) {
    const selectedProject = availableProjects.value.find((p) => p.encryptId === encCorpId);
    if (selectedProject) {
        modelForm.value.corpName = selectedProject.name;
    }
}

// 新建模型
function resetModelForm() {
    modelForm.value = {
        encCorpId: '',
        corpName: '',
        name: '',
        status: 0,
        keyPotentialQualityList: [],
        positionQualityModelMatchList: [],
    };
}

// 编辑模型
async function editModel(model: ModelItem, index: number) {
    editingModelIndex.value = index;

    try {
        if (model.encId && model.encCorpId) {
            // 加载模型详细数据
            const { code, data } = await _getCorpModelDetail({
                encCorpId: model.encCorpId,
                productId: 21,
                encId: model.encId,
            });

            if (code === 0 && data) {
                modelForm.value = {
                    ...model,
                    keyPotentialQualityList: data.keyPotentialQualityList || [],
                    positionQualityModelMatchList: data.positionQualityModelMatchList || [],
                };
            } else {
                modelForm.value = { ...model };
            }
        } else {
            modelForm.value = { ...model };
        }

        showAddModelDialog.value = true;
    } catch (error) {
        Toast.danger('加载模型详情失败');
        modelForm.value = { ...model };
        showAddModelDialog.value = true;
    }
}

// 保存模型
async function saveModel() {
    const valid = await modelFormRef.value.validate();
    if (!valid) return;

    try {
        const params = {
            encCorpId: modelForm.value.encCorpId!,
            productId: 21, // 五维性格测评2.0的产品ID
            name: modelForm.value.name,
            keyPotentialQualityList: modelForm.value.keyPotentialQualityList || [],
            positionQualityModelMatchList: modelForm.value.positionQualityModelMatchList || [],
        };

        if (editingModelIndex.value !== -1 && modelForm.value.encId) {
            // 编辑模式，添加encId
            (params as any).encId = modelForm.value.encId;
        }

        const { code } = await _saveCorpModel(params);
        if (code === 0) {
            showAddModelDialog.value = false;
            editingModelIndex.value = -1;
            resetModelForm();

            // 重新加载模型列表
            await loadModelList();

            Toast.success('保存成功');
        }
    } catch (error: any) {
        Toast.danger(error?.message || '保存失败');
    }
}

// 取消编辑
function cancelEditModel() {
    showAddModelDialog.value = false;
    editingModelIndex.value = -1;
    resetModelForm();
}

// 切换模型状态
async function toggleModelStatus(model: ModelItem, index: number) {
    if (!model.encId) {
        Toast.danger('模型ID不存在');
        return;
    }

    try {
        const newStatus = model.status === 0 ? 1 : 0;
        const { code } = await _updateCorpModelStatus({
            encId: model.encId,
            status: newStatus.toString(),
        });

        if (code === 0) {
            modelList.value[index].status = newStatus;
            Toast.success('状态更新成功');
        }
    } catch (error) {
        Toast.danger('状态更新失败');
    }
}

// 删除模型
function deleteModel(index: number) {
    Dialog.open({
        type: 'warning',
        title: '删除确认',
        content: '确定要删除这个模型吗？',
        confirm() {
            modelList.value.splice(index, 1);
            Toast.success('删除成功');
        },
    });
}

// 加载模型列表
async function loadModelList() {
    try {
        const { code, data } = await _getCorpModelList({
            productId: 21, // 五维性格测评2.0的产品ID
            page: 1,
            pageSize: 100,
        });

        if (code === 0) {
            modelList.value = data.list || [];
        }
    } catch (error) {
        Toast.danger('加载模型列表失败');
    }
}

// 加载项目列表
async function loadProjects() {
    try {
        const { code, data } = await _getProjectOptions();
        if (code === 0) {
            availableProjects.value = data || [];
        }
    } catch (error) {
        Toast.danger('加载项目列表失败');
        // 使用模拟数据作为备选
        availableProjects.value = [
            { encryptId: '1', name: '项目A' },
            { encryptId: '2', name: '项目B' },
            { encryptId: '3', name: '项目C' },
        ];
    }
}

onMounted(() => {
    loadProjects();
    loadModelList();
});
</script>

<style lang="less" scoped>
.enterprise-custom-model-config {
    padding: 20px 0;
}

.sub-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.model-config-section {
    margin: 20px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0 0 10px 0;
    }
}

.config-placeholder {
    padding: 40px;
    text-align: center;
    color: #86909c;
    font-size: 14px;
}
</style>
