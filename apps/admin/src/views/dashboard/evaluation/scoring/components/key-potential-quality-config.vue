<template>
    <div class="key-potential-quality-config">
        <div class="sub-title">
            <h4>关键潜在素质分数计算</h4>
        </div>

        <!-- 关键潜在素质分数权重表 -->
        <div class="section-title">
            <h5>1、关键潜在素质分数权重表</h5>
        </div>

        <b-table border fitWidth :columns="weightColumns" :tableData="weightTableData">
            <template v-for="(col, index) in weightColumns" :key="col.field" #[`th-${col.field}`]="{ raw }">
                <div v-if="index === 0" class="reference">
                    <div style="white-space: nowrap">二级维度</div>
                    <b-button v-if="configGroups.length < 5" type="text" size="xsmall" status="primary" font="medium" @click="addConfigGroup"> + 添加一列 </b-button>
                </div>
                <div v-else class="reference">
                    <FormField :field="`configGroups.${index - 1}.name`" hideLabel :rules="[{ required: true, message: '请填写配置组名称' }]">
                        <b-input v-model.trim="configGroups[index - 1].name" placeholder="请输入配置组名称" :maxLength="10" />
                    </FormField>
                    <b-action v-if="configGroups.length > 1" @click="removeConfigGroup(configGroups[index - 1].id)">
                        <SvgIcon name="delete-2" />
                    </b-action>
                </div>
            </template>
            <template v-for="(col, index) in weightColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                <div v-if="index === 0">
                    {{ raw.dimensionName }}
                </div>
                <div v-else class="center">
                    <div style="max-width: 120px">
                        <FormField :field="`weightTableData.${$index}.${col.field}`" hideLabel :rules="[{ required: true, message: '请输入权重' }]">
                            <b-input-number v-model="raw[col.field]" placeholder="请输入权重" hideButton :min="0" :max="1" :precision="4" @change="validateWeightSum" />
                        </FormField>
                    </div>
                </div>
            </template>
        </b-table>

        <div v-if="weightSumError" class="error-tip">权重和必须等于 1</div>

        <!-- 关键潜在素质常模 -->
        <div class="section-title">
            <h5>2、关键潜在素质常模</h5>
        </div>

        <b-table border fitWidth :columns="normColumns" :tableData="normTableData">
            <template v-for="(col, index) in normColumns" :key="col.field" #[`th-${col.field}`]>
                <div v-if="index === 0" class="reference">
                    <div style="white-space: nowrap">二级维度</div>
                </div>
                <div v-else class="reference">
                    <div style="white-space: nowrap">{{ col.label }}</div>
                </div>
            </template>
            <template v-for="(col, index) in normColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                <div v-if="index === 0">
                    {{ raw.dimensionName }}
                </div>
                <div v-else class="center">
                    <div style="max-width: 120px">
                        <FormField :field="`normTableData.${$index}.${col.field}`" hideLabel :rules="[{ required: true, message: '请填写常模数据' }]">
                            <b-input-number
                                v-model="raw[col.field]"
                                :placeholder="col.field.includes('avgScore') ? '请填写0-1000内数值，最多4位小数' : '请填写1000以内正数，最多4位小数'"
                                hideButton
                                :min="col.field.includes('stdDev') ? 0.0001 : 0"
                                :max="1000"
                                :precision="4"
                            />
                        </FormField>
                    </div>
                </div>
            </template>
        </b-table>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { FormField } from '@crm/web-components';
import { useRoute } from 'vue-router';
import { _getDimensionQuery } from '@/services/api/scoring';

interface DimensionItem {
    encryptId: string;
    name: string;
    dimensionLevel: number;
}

interface ConfigGroup {
    id: string;
    name: string;
}

const props = defineProps<{
    modelValue: any;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: any];
}>();

const $route = useRoute();

// 二级维度数据
const dimensionList = ref<DimensionItem[]>([]);

// 配置组数据
const configGroups = ref<ConfigGroup[]>([{ id: '1', name: '配置组1' }]);

// 权重表数据 - 基于二级维度和配置组
const weightTableData = ref<any[]>([]);

// 常模表数据 - 基于二级维度和配置组
const normTableData = ref<any[]>([]);

// 动态权重表列配置
const weightColumns = ref([
    {
        label: '二级维度',
        field: 'dimension',
        width: 200,
    },
]);

// 动态常模表列配置
const normColumns = ref([
    {
        label: '二级维度',
        field: 'dimension',
        width: 200,
    },
]);

// 权重和验证
const weightSumError = ref(false);

// 获取二级维度数据
async function getDimensionList() {
    try {
        const { code, data } = await _getDimensionQuery({
            productId: $route.query?.productId || 21, // 五维性格测评2.0的产品ID
            dimensionLevel: 2, // 只获取二级维度
            flat: true,
        });
        if (code === 0) {
            dimensionList.value = data || [];
            initTableData();
        }
    } catch (e: any) {
        console.error('获取维度数据失败:', e);
    }
}

// 初始化表格数据
function initTableData() {
    // 初始化权重表数据
    weightTableData.value = dimensionList.value.map((dimension) => {
        const rowData: any = {
            dimensionName: dimension.name,
            encryptDimensionId: dimension.encryptId,
        };

        // 为每个配置组添加权重字段
        configGroups.value.forEach((group) => {
            rowData[`weight_${group.id}`] = 0;
        });

        return rowData;
    });

    // 初始化常模表数据
    normTableData.value = dimensionList.value.map((dimension) => {
        const rowData: any = {
            dimensionName: dimension.name,
            encryptDimensionId: dimension.encryptId,
        };

        // 为每个配置组添加常模字段
        configGroups.value.forEach((group) => {
            rowData[`avgScore_${group.id}`] = 0;
            rowData[`stdDev_${group.id}`] = 0;
        });

        return rowData;
    });

    updateColumns();
}

// 更新列配置
function updateColumns() {
    // 更新权重表列配置
    weightColumns.value = [
        {
            label: '二级维度',
            field: 'dimension',
            width: 200,
        },
        ...configGroups.value.map((group) => ({
            label: group.name,
            field: `weight_${group.id}`,
            width: 160,
        })),
    ];

    // 更新常模表列配置
    normColumns.value = [
        {
            label: '二级维度',
            field: 'dimension',
            width: 200,
        },
        ...configGroups.value.flatMap((group) => [
            {
                label: `${group.name}-平均分`,
                field: `avgScore_${group.id}`,
                width: 120,
            },
            {
                label: `${group.name}-标准差`,
                field: `stdDev_${group.id}`,
                width: 120,
            },
        ]),
    ];
}

// 添加配置组
function addConfigGroup() {
    if (configGroups.value.length >= 5) return;

    const newId = Date.now().toString();
    const newGroup = {
        id: newId,
        name: `配置组${configGroups.value.length + 1}`,
    };

    configGroups.value.push(newGroup);

    // 为现有数据添加新配置组的字段
    weightTableData.value.forEach((row) => {
        row[`weight_${newId}`] = 0;
    });

    normTableData.value.forEach((row) => {
        row[`avgScore_${newId}`] = 0;
        row[`stdDev_${newId}`] = 0;
    });

    updateColumns();
}

// 删除配置组
function removeConfigGroup(groupId: string) {
    if (configGroups.value.length <= 1) return;

    const index = configGroups.value.findIndex((group) => group.id === groupId);
    if (index > -1) {
        configGroups.value.splice(index, 1);

        // 从数据中移除对应字段
        weightTableData.value.forEach((row) => {
            delete row[`weight_${groupId}`];
        });

        normTableData.value.forEach((row) => {
            delete row[`avgScore_${groupId}`];
            delete row[`stdDev_${groupId}`];
        });

        updateColumns();
    }
}

// 验证权重和
function validateWeightSum() {
    configGroups.value.forEach((group) => {
        const sum = weightTableData.value.reduce((acc: number, row: any) => acc + (row[`weight_${group.id}`] || 0), 0);
        weightSumError.value = Math.abs(sum - 1) > 0.0001;
    });
}

// 组件挂载时获取数据
onMounted(() => {
    getDimensionList();
});
</script>

<style lang="less" scoped>
.key-potential-quality-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}

.reference {
    gap: var(--size-3);
    .w-full;
    .group;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
