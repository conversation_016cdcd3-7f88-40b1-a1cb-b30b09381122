<template>
    <b-input :modelValue="internalValue" @update:modelValue="handleInput" placeholder="输入1-5" @blur="formatInput" @focus="handleFocus" />
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';

const props = defineProps<{ modelValue: number[] | undefined }>();
const emit = defineEmits(['update:modelValue']);

const internalValue = ref('');
let isInternalUpdate = false; // Prevents watch feedback loop

// Watch for external changes to modelValue
watch(
    () => props.modelValue,
    (newValue) => {
        if (isInternalUpdate) {
            // Skip update if change was triggered by this component's emit
            return;
        }
        // Update internalValue based on the new prop value
        if (newValue && newValue.length > 0) {
            internalValue.value = newValue.join('、');
            // External updates should NOT have trailing dunhao by default
        } else {
            internalValue.value = '';
        }
    },
    { immediate: true, deep: true }
);

function handleInput(value: string) {
    // 1. Clean input
    let displayValue = value.replace(/[^1-5、]/g, '');
    displayValue = displayValue.replace(/、、+/g, '、'); // Remove consecutive
    if (displayValue.startsWith('、')) {
        // Remove leading
        displayValue = displayValue.substring(1);
    }

    // 2. Extract number strings (allows duplicates temporarily for display)
    const currentNumbersStr = displayValue
        .split('、')
        .map((n) => n.trim())
        .filter((n) => n && /^[1-5]$/.test(n)); // Filter out empty strings from split and invalid numbers

    // 3. Limit display based on count (max 5 numbers)
    if (currentNumbersStr.length > 5) {
        // Truncate the string representation if too many numbers entered
        displayValue = currentNumbersStr.slice(0, 5).join('、');
        // After truncating to 5, ensure no trailing dunhao
        internalValue.value = displayValue; // Update display immediately
    } else {
        // Add/Remove trailing dunhao for display during input (< 5 numbers)
        const endsWithDunhao = displayValue.endsWith('、');
        // Check if the last *actual character* is a number (could be empty string after split)
        const lastSegmentIsNumber = displayValue.length > 0 && /^[1-5]$/.test(displayValue[displayValue.length - (endsWithDunhao ? 2 : 1)]);

        if (currentNumbersStr.length < 5 && lastSegmentIsNumber && !endsWithDunhao) {
            displayValue += '、';
        } else if (currentNumbersStr.length >= 5 && endsWithDunhao) {
            // Handles reaching 5 numbers or pasting "1、2、3、4、5、"
            displayValue = displayValue.slice(0, -1);
        }
        internalValue.value = displayValue; // Update display
    }

    // 4. Prepare and emit unique numbers based on the potentially corrected internalValue
    const numbersToEmit = internalValue.value
        .split('、')
        .map((n) => n.trim())
        .filter((n) => n && /^[1-5]$/.test(n)) // Ensure filtering here too
        .map(Number);
    const uniqueNumbersToEmit = [...new Set(numbersToEmit)];

    // 5. Emit only if the unique model actually changed
    const currentModelValue = props.modelValue || [];
    const modelChanged = uniqueNumbersToEmit.length !== currentModelValue.length || uniqueNumbersToEmit.some((num, index) => num !== currentModelValue[index]);

    if (modelChanged) {
        isInternalUpdate = true;
        emit('update:modelValue', uniqueNumbersToEmit);
        nextTick(() => {
            isInternalUpdate = false;
        });
    }
}

// On Focus: Add trailing dunhao if appropriate based on current modelValue
function handleFocus() {
    const currentModelValue = props.modelValue || [];
    if (currentModelValue.length > 0 && currentModelValue.length < 5) {
        if (!internalValue.value.endsWith('、')) {
            internalValue.value += '、';
        }
    }
}

// On Blur: Sync display strictly with the definitive modelValue (no trailing dunhao)
function formatInput() {
    const currentModelValue = props.modelValue || [];
    if (currentModelValue.length > 0) {
        internalValue.value = currentModelValue.join('、');
    } else {
        internalValue.value = '';
    }
}
</script>
