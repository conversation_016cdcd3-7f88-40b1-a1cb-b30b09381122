<template>
    <div class="team-role-config">
        <div class="sub-title">
            <h4>团队角色分数计算</h4>
        </div>

        <!-- 团队角色分数权重表 -->
        <div class="section-title">
            <h5>1、团队角色分数权重表</h5>
        </div>

        <b-table border fitWidth :columns="weightColumns" :tableData="weightTableData">
            <template v-for="(col, index) in weightColumns" :key="col.field" #[`th-${col.field}`]="{ raw }">
                <div v-if="index === 0" class="reference">
                    <div style="white-space: nowrap">团队角色</div>
                </div>
                <div v-else class="reference">
                    <div style="white-space: nowrap">权重</div>
                </div>
            </template>
            <template v-for="(col, index) in weightColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                <div v-if="index === 0">
                    {{ raw.roleName }}
                </div>
                <div v-else class="center">
                    <div style="max-width: 120px">
                        <FormField
                            :field="`weightTableData.${$index}.weight`"
                            hideLabel
                            :rules="[{ required: true, message: '请输入权重' }]"
                        >
                            <b-input-number
                                v-model="raw.weight"
                                placeholder="请输入权重"
                                hideButton
                                :min="0"
                                :max="1"
                                :precision="4"
                                @change="validateWeightSum"
                            />
                        </FormField>
                    </div>
                </div>
            </template>
        </b-table>

        <div v-if="weightSumError" class="error-tip">权重和必须等于 1</div>

        <!-- 团队角色常模 -->
        <div class="section-title">
            <h5>2、团队角色常模</h5>
        </div>

        <b-table border fitWidth :columns="normColumns" :tableData="normTableData">
            <template v-for="(col, index) in normColumns" :key="col.field" #[`th-${col.field}`]>
                <div v-if="index === 0" class="reference">
                    <div style="white-space: nowrap">团队角色</div>
                </div>
                <div v-else-if="index === 1" class="reference">
                    <div style="white-space: nowrap">常模平均分</div>
                </div>
                <div v-else class="reference">
                    <div style="white-space: nowrap">常模标准差</div>
                </div>
            </template>
            <template v-for="(col, index) in normColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                <div v-if="index === 0">
                    {{ raw.roleName }}
                </div>
                <div v-else-if="index === 1" class="center">
                    <div style="max-width: 120px">
                        <FormField
                            :field="`normTableData.${$index}.avgScore`"
                            hideLabel
                            :rules="[{ required: true, message: '请填写常模平均分' }]"
                        >
                            <b-input-number
                                v-model="raw.avgScore"
                                placeholder="请填写0-1000内数值，最多4位小数"
                                hideButton
                                :min="0"
                                :max="1000"
                                :precision="4"
                            />
                        </FormField>
                    </div>
                </div>
                <div v-else class="center">
                    <div style="max-width: 120px">
                        <FormField
                            :field="`normTableData.${$index}.stdDev`"
                            hideLabel
                            :rules="[{ required: true, message: '请填写常模标准差' }]"
                        >
                            <b-input-number
                                v-model="raw.stdDev"
                                placeholder="请填写1000以内正数，最多4位小数"
                                hideButton
                                :min="0.0001"
                                :max="1000"
                                :precision="4"
                            />
                        </FormField>
                    </div>
                </div>
            </template>
        </b-table>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { FormField } from '@crm/web-components';

interface TeamRoleItem {
    id: string;
    roleName: string;
    weight: number;
    avgScore: number;
    stdDev: number;
}

const props = defineProps<{
    modelValue: any;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: any];
}>();

// 默认的九个团队角色
const DEFAULT_TEAM_ROLES = ['实干者', '协调者', '推进者', '创新者', '信息者', '监督者', '凝聚者', '完善者', '专业师'];

// 权重表数据 - 固定九个角色
const weightTableData = ref<TeamRoleItem[]>([]);

// 常模表数据 - 对应九个角色
const normTableData = ref<TeamRoleItem[]>([]);

// 权重表列配置
const weightColumns = ref([
    {
        label: '团队角色',
        field: 'roleName',
        width: 200,
    },
    {
        label: '权重',
        field: 'weight',
        width: 150,
    },
]);

// 常模表列配置
const normColumns = ref([
    {
        label: '团队角色',
        field: 'roleName',
        width: 200,
    },
    {
        label: '常模平均分',
        field: 'avgScore',
        width: 150,
    },
    {
        label: '常模标准差',
        field: 'stdDev',
        width: 150,
    },
]);

// 权重和验证
const weightSumError = ref(false);

// 初始化表格数据
function initTableData() {
    // 初始化权重表数据
    weightTableData.value = DEFAULT_TEAM_ROLES.map((roleName, index) => ({
        id: (index + 1).toString(),
        roleName,
        weight: 0,
        avgScore: 0,
        stdDev: 0,
    }));

    // 初始化常模表数据
    normTableData.value = DEFAULT_TEAM_ROLES.map((roleName, index) => ({
        id: (index + 1).toString(),
        roleName,
        weight: 0,
        avgScore: 0,
        stdDev: 0,
    }));
}

// 验证权重和
function validateWeightSum() {
    const sum = weightTableData.value.reduce((acc: number, item: TeamRoleItem) => acc + (item.weight || 0), 0);
    weightSumError.value = Math.abs(sum - 1) > 0.0001;
}

// 组件挂载时初始化数据
onMounted(() => {
    initTableData();
});
</script>

<style lang="less" scoped>
.team-role-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}

.reference {
    gap: var(--size-3);
    .w-full;
    .group;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
