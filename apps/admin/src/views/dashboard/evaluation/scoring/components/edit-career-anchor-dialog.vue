<template>
    <b-dialog
        v-model="visible"
        width="600px"
        :layerClosable="false"
        :enableEscClose="false"
        :confirmIsLoading="loading"
        :beforeConfirm="handleBeforeConfirm"
        @close="handleCancel"
        @cancel="handleCancel"
    >
        <template #title> 编辑职业锚 </template>
        <b-form :model="formData" layout="vertical" @submit.prevent>
            <b-form-item label="职业锚名称" field="jobName" asteriskPosition="end">
                <b-input v-model.trim="formData.jobName" disabled />
            </b-form-item>
            <b-form-item label="职业锚计分表" class="form-scorecard-item">
                <div class="remark">职业锚得分=多个二级维度报告分的平均分；正向计分=二级维度报告分，反向计分=11-二级维度报告分</div>
            </b-form-item>
            <b-table
                ref="formTableRef"
                class="scorecard"
                v-model:tableData="careerAnchorScoreList"
                :columns="columns"
                stickyHeader
                tableLayoutFixed
                :formMode="{ scrollToFirstError: true }"
                :border="true"
                :scroll="{ y: '100%' }"
            >
                <template #td-encryptDimensionId="{ raw }">
                    <b-form-item noStyle hideAsterisk field="encryptDimensionId" :rules="[{ required: true, message: '维度不能为空' }]">
                        <b-tree-select
                            v-model="raw.encryptDimensionId"
                            :fieldNames="{ key: 'encryptId', title: 'name', children: 'children' }"
                            :data="dimensionList"
                            placeholder="请选择"
                            pathLabel
                        ></b-tree-select>
                    </b-form-item>
                </template>
                <template #td-direction="{ raw }">
                    <b-form-item noStyle hideAsterisk field="direction" :rules="[{ required: true, message: '正反向计分不能为空' }]">
                        <b-select v-model="raw.direction" placeholder="请选择">
                            <b-option v-for="(item, index) in directionList" :key="index" :label="item.name" :value="item.id" />
                        </b-select>
                    </b-form-item>
                </template>
                <template #td-operation="{ $rowIndex }">
                    <b-button v-if="careerAnchorScoreList.length > 1" status="danger" size="small" @click="onDeleted($rowIndex)">删除</b-button>
                </template>
            </b-table>
            <b-button v-if="careerAnchorScoreList.length < 10" status="primary" type="outline" class="add-score-btn" @click="onAddScore">添加计分</b-button>
        </b-form>
    </b-dialog>
</template>

<script setup lang="ts">
import { _saveCareerAnchorInfo, _getDimensionV2List } from '@/services/api/scoring';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { PCode } from '@crm/biz-exam-product';
import { cloneDeep } from 'es-toolkit';

defineOptions({
    name: 'EditCareerAnchorDialog',
});
const props = defineProps({
    row: {
        type: Object,
        default: () => ({}),
    },
});
const emits = defineEmits(['close', 'success']);
const $route = useRoute();
const formData = reactive({
    jobName: '',
});
const columns = [
    {
        label: '维度',
        field: 'encryptDimensionId',
    },
    {
        label: '正反向计分',
        field: 'direction',
    },
    {
        label: '操作',
        field: 'operation',
        width: 120,
    },
];
const visible = ref(true);
function handleCancel() {
    emits('close');
}
const loading = ref(false);
const formTableRef = ref<any>(null);
async function handleBeforeConfirm() {
    const res = await formTableRef.value.validate();
    if (res.some(Boolean)) {
        return false;
    }
    try {
        loading.value = true;
        const { code } = await _saveCareerAnchorInfo({
            encryptId: props.row?.encryptId,
            productId: $route.query?.productId,
            careerAnchorScoreList: careerAnchorScoreList.value,
        });
        if (code === 0) {
            emits('close');
            emits('success');
        }
        return code === 0;
    } catch (e: any) {
        Toast.danger(e?.message);
        return false;
    } finally {
        loading.value = false;
    }
}
const careerAnchorScoreList = ref<any>([]);
const directionList = [
    {
        name: '负向',
        id: 0,
    },
    {
        name: '正向',
        id: 1,
    },
];
const onAddScore = () => {
    careerAnchorScoreList.value.push({});
};
const onDeleted = (index: number) => {
    careerAnchorScoreList.value.splice(index, 1);
};
const dimensionList = ref<any>([]);
const getDimensionV2List = async () => {
    try {
        const { code, data } = await _getDimensionV2List({
            productId: PCode.MA,
            dimensionStatus: 0, // 维度启用状态0-启用、1-禁用
        });
        if (code === 0) {
            dimensionList.value = data.map((item: any) => ({ ...item, selectable: false }));
        }
    } catch (e: any) {
        Toast.danger(e.message);
    }
};
getDimensionV2List();
onMounted(() => {
    if (props.row?.encryptId) {
        formData.jobName = props.row?.jobName;
        careerAnchorScoreList.value = cloneDeep(props.row?.careerAnchorScoreList || []);
    }
});
</script>
<style lang="less" scoped>
:deep(.b-form-item-label) {
    font-weight: 600;
    font-size: 15px;
}
.form-scorecard-item {
    .remark {
        font-size: 13px;
    }
    :deep(.b-form-item-content-flex) {
        display: block;
    }
    :deep(.scorecard) {
        margin-top: 15px;
    }
}
.add-score-btn {
    width: 100px;
    margin-top: 15px;
}
:deep(.b-select-view-value) {
    overflow: unset;
    white-space: normal;
}
</style>
