<template>
    <b-layout direction="vertical">
        <b-section type="title" justify="between">
            <b-title size="large"> 岗位列表 </b-title>
            <b-button type="primary" @click="handleEditJob">
                <b-space gap="5px"> <SvgIcon name="hicon-add" width="16" height="16" /><span>新增岗位</span> </b-space>
            </b-button>
        </b-section>
        <b-section type="list" direction="vertical">
            <b-table
                ref="listTable"
                :queryFun="getJobList"
                stickyHeader
                fullHeight
                :border="true"
                tableLayoutFixed
                :columns="JOB_LIST_COLUMNS"
                :pagination="{ pageType: 'server', pageSize: 10, showTotal: true, showSizer: true, pageSizeOpts: [10, 20, 30, 50, 100] }"
            >
                <template #td-status="{ raw }">
                    <div class="status-indicator" :class="[`status-indicator-${raw.status}`]" />
                    {{ STATUS_MAP[raw.status] }}
                </template>
                <template #td-operation="{ raw }">
                    <b-link status="primary" href="javascript:;" @click.prevent="() => handleEditJob(raw)"> 编辑 </b-link>
                    <b-link :loading="raw.statusLoading" status="primary" style="margin-left: 20px" @click.prevent="() => handleChangeStatus(raw)">
                        {{ raw.status === ENABLE_STATUS_CODE ? '停用' : '启用' }}
                    </b-link>
                </template>
            </b-table>
        </b-section>
        <EditJobDialog v-if="showEditJobDialog" :row="row" @close="closeDialog" />
    </b-layout>
</template>

<script setup lang="ts">
import type { ITablePagination } from '@boss/design';
import { _getJobList, _updateStatus } from '@/services/api/scoring';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { DISABLED_STATUS_CODE, ENABLE_STATUS_CODE, JOB_LIST_COLUMNS, STATUS_MAP } from '../constant';
import EditJobDialog from './edit-job-dialog.vue';

defineOptions({
    name: 'JobManage',
});
const $router = useRouter();
const $route = useRoute();

const listTable = ref();
function closeDialog(isUpdateData) {
    showEditJobDialog.value = false;
    if (isUpdateData) {
        listTable.value.query({ keepPage: !!row.value.encryptId });
    }
}
/**
 * 新增or编辑岗位信息
 */
const row = ref();
const showEditJobDialog = ref(false);
function handleEditJob(data) {
    row.value = data;
    showEditJobDialog.value = true;
}
function handleChangeStatus(raw) {
    const params = {
        productId: $route.query?.productId,
        encryptId: raw.encryptId,
        status: raw.status === ENABLE_STATUS_CODE ? DISABLED_STATUS_CODE : ENABLE_STATUS_CODE,
    };
    if (raw.status === ENABLE_STATUS_CODE) {
        Dialog.open({
            type: 'warning',
            title: '请确认',
            confirmIsLoading: statusLoading.value,
            content: () => '确定停用此岗位吗?',
            confirm() {
                updateStatus(params, raw);
            },
        });
        return;
    }
    updateStatus(params, raw);
}
const statusLoading = ref(false);
async function updateStatus(params, raw) {
    try {
        statusLoading.value = true;
        raw.statusLoading = true;
        const { code } = await _updateStatus(params);
        if (code === 0) {
            Toast.success('操作成功');
            listTable.value.query({ keepPage: true });
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    } finally {
        raw.statusLoading = false;
        statusLoading.value = false;
    }
}
const formRef = ref();
const loading = ref(false);
async function getJobList({ page, pageSize }: Partial<ITablePagination>) {
    try {
        const params: any = { productId: $route.query?.productId };
        const { code, data } = await _getJobList({ ...params, page, pageSize });
        if (code === 0) {
            return {
                total: data.total,
                data: data.list,
                pageSize,
            } as any;
        }
    } catch (e) {
        Toast.danger(e?.message);
    }
}
onMounted(() => {
    listTable.value.query();
});
</script>

<style lang="less" scoped>
.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    &-0 {
        background-color: var(--success-color-6);
    }
    &-1 {
        background-color: var(--danger-color-5);
    }
}
</style>
