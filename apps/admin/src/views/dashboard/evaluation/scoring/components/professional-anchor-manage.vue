<template>
    <b-layout direction="vertical">
        <b-section type="title" justify="between">
            <b-title size="large"> 职业锚列表 </b-title>
        </b-section>
        <b-section type="list" direction="vertical" v-loading="loading">
            <b-table :tableData="dataSource" ref="listTable" stickyHeader fullHeight :border="true" tableLayoutFixed :columns="PROFESSIONAL_ANCHOR_LIST_COLUMNS">
                <template #td-operation="{ raw }">
                    <b-link status="primary" href="javascript:;" @click.prevent="handleEditJob(raw)"> 编辑 </b-link>
                </template>
            </b-table>
        </b-section>
        <EditCareerAnchorDialog v-if="showEditJobDialog" :row="row" @close="closeDialog" @success="getCareerAnchorList()" />
    </b-layout>
</template>

<script setup lang="ts">
import { _getCareerAnchorList } from '@/services/api/scoring';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { PROFESSIONAL_ANCHOR_LIST_COLUMNS } from '../constant';
import EditCareerAnchorDialog from './edit-career-anchor-dialog.vue';

defineOptions({
    name: 'ProfessionalAnchorManage',
});
const $router = useRouter();
const $route = useRoute();

const listTable = ref();
function closeDialog() {
    showEditJobDialog.value = false;
}
/**
 * 编辑职业锚
 */
const row = ref();
const showEditJobDialog = ref(false);
function handleEditJob(data: any) {
    row.value = data;
    showEditJobDialog.value = true;
}
const loading = ref(false);
const dataSource = ref([]);
async function getCareerAnchorList() {
    loading.value = true;
    try {
        const params: any = { productId: $route.query?.productId };
        const { code, data } = await _getCareerAnchorList(params);
        if (code === 0) {
            dataSource.value = data;
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    } finally {
        loading.value = false;
    }
}
getCareerAnchorList();
</script>
