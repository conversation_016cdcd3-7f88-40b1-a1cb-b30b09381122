<template>
    <b-dialog
        v-model="visible"
        width="560px"
        :layerClosable="false"
        :enableEscClose="false"
        :confirmIsLoading="loading"
        :beforeConfirm="handleBeforeConfirm"
        @cancel="handleCancel"
    >
        <template #title> {{ row.encryptId ? '编辑' : '新增' }}岗位 </template>
        <b-form ref="formRef" :model="formData" layout="vertical" @submit.prevent>
            <b-form-item label="岗位" field="jobName" asteriskPosition="end" :rules="[{ required: true, type: 'string', message: '请输入岗位' }]">
                <b-input v-model.trim="formData.jobName" placeholder="请输入" :maxLength="10" showWordLimit />
            </b-form-item>
        </b-form>
    </b-dialog>
</template>

<script setup lang="ts">
import { _saveJobInfo } from '@/services/api/scoring';
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

defineOptions({
    name: 'EditJobDialog',
});
const props = defineProps({
    row: {
        type: Object,
        default: () => ({}),
    },
});
const emits = defineEmits(['close']);
const $route = useRoute();
const formData = reactive({
    jobName: '',
});
const visible = ref(true);
function handleCancel() {
    emits('close');
}
const formRef = ref(null);
const loading = ref(false);
async function handleBeforeConfirm() {
    const res = await formRef.value?.validate();
    if (res) {
        return false;
    }
    try {
        loading.value = true;
        const { code } = await _saveJobInfo({
            encryptId: props.row?.encryptId,
            productId: $route.query?.productId,
            jobName: formData.jobName,
        });
        if (code === 0) {
            emits('close', true);
        }
        return code === 0;
    } catch (e: any) {
        Toast.danger(e?.message);
        return false;
    } finally {
        loading.value = false;
    }
}
if (props.row?.encryptId) {
    formData.jobName = props.row?.jobName;
}
</script>
