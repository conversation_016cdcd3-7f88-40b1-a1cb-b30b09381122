<template>
    <b-layout type="content">
        <b-layout v-loading="showLoading" direction="vertical">
            <div class="page-title">{{ mode === 'edit' ? '编辑' : '新增' }}模板</div>
            <div class="form-section">
                <div class="section-title">基础信息</div>
                <b-form ref="formRefBase" :model="formDataBase" layout="vertical" gentleValidation>
                    <!-- 基础信息（模板名称、测评产品、指导语） -->
                    <BaseInfoForm :mode="mode" @changeProduct="changeProduct" />
                    <!-- 组合方案-（目前支持校招组合方案，后期可能扩展） -->
                    <!-- 新质人才测评方案 -->
                    <template v-if="isMentalHealthTeamCombination || isNewTalentCombination">
                        <div class="section-title">试卷模板</div>
                        <MentalHealthTeamCombination v-model:value="formDataBase.paperTemplateList" :productId="formDataBase.productId" />
                    </template>
                    <!-- 金融人才胜任力测 -->
                    <FinancePersonnel
                        v-else-if="isFinancePersonnelProduct"
                        v-model="formDataBase"
                        :mode="mode"
                        :questionDetailInfoList="questionDetailInfoList"
                        @updateDataSnapshot="updateDataSnapshot"
                    />
                    <!-- 职业动机测评 -->
                    <MotivationAssessment
                        v-else-if="isMotivationAssessmentProduct"
                        v-model="formDataBase"
                        :mode="mode"
                        :questionDetailInfoList="questionDetailInfoList"
                        @updateDataSnapshot="updateDataSnapshot"
                    />
                    <!-- 职业动机测评 -->
                    <DiscAssessment
                        v-else-if="isDiscProduct"
                        v-model="formDataBase"
                        :mode="mode"
                        :questionDetailInfoList="questionDetailInfoList"
                        @updateDataSnapshot="updateDataSnapshot"
                    />
                    <template v-else>
                        <!-- 职业韧性测评、高潜人才识别测评、敬业度调研测评、笔迹职业适应力测评产品 -->
                        <ProfessionalWillfulness v-if="showAdviseAnswerTimeComponent" v-model="formDataBase" :productId="formDataBase.productId" />
                        <!-- 领导力情景测评、16T职业性格测评、职业心理健康测评 -->
                        <LeadershipSkills v-if="isLeadershipSkillsProduct || is16TProfessionalProduct || isMentalHealthTeam || isDiscProduct" v-model="formDataBase" />
                        <!-- 高潜人才识别测评、敬业度调研测评 -->
                        <Hots v-if="isHotsProduct" v-model="formDataBase" :questionDetailInfoList="questionDetailInfoList" />
                        <!-- 认知能力测评 -->
                        <Caa v-if="isCaaProduct" v-model="formDataBase" />
                        <!-- 五维性格测评2.0 -->
                        <OEA2 v-if="isOEA2Product" v-model="formDataBase" :questionDetailInfoList="questionDetailInfoList" />
                        <b-form-item
                            v-if="showEvaluationDimensionSelect"
                            label="测评维度"
                            required
                            class="b-form-item-wrap"
                            asteriskPosition="end"
                            field="dimension"
                            :rules="[{ type: 'array', required: true, message: '请选择测评维度' }]"
                        >
                            <div class="dimension-select-wrap">
                                <b-cascader
                                    v-model="formDataBase.dimension"
                                    :triggerProps="{ contentClass: 'template-cascader-content' }"
                                    :fieldNames="{ value: 'encryptId', label: 'dimensionName' }"
                                    :defaultValue="formDataBase.dimension"
                                    :options="dimensionList"
                                    expandTrigger="hover"
                                    placeholder="请选择"
                                    allowSearch
                                    multiple
                                    @change="changeDimension"
                                />
                            </div>
                            <div class="dimension-hint">
                                <div v-for="(item, index) of conflictTips" :key="index" class="hint-item">
                                    <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                                    <span class="hint-text">{{ item }}</span>
                                </div>
                            </div>
                        </b-form-item>
                    </template>
                </b-form>
                <!-- 测评维度表格配置（维度配置、维度互斥、维度权重） -->
                <DimensionConfigTable v-if="showEvaluationDimensionTable" ref="formRefDimensionConfigTable" />
                <!-- 作答配置 -->
                <template v-if="showAnswerConfig">
                    <div class="section-title">作答配置</div>
                    <AnswerConfig ref="formRefAnswer" v-model:value="formDataAnswer" :productId="formDataBase.productId" />
                </template>
            </div>
            <div class="btn-wrap">
                <div />
                <div>
                    <b-button type="outline" @click="cancel"> 取消 </b-button>
                    <b-button type="primary" @click="save"> 保存 </b-button>
                </div>
            </div>
        </b-layout>
    </b-layout>
</template>

<script setup lang="ts" name="TemplateDetail">
import { _getConflictDimensionTips, _getDimensionDetailInfo, _getDimensionOptionContainsDetailInfo, _getTemplateInfo, _saveTemplate } from '@/services/api/template';
import { formValidateSort } from '@/utils/index';
import { PCode } from '@crm/biz-exam-product';
import { cloneDeep, isEqual } from 'es-toolkit';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import BaseInfoForm from './component/base-info-form.vue';
import AnswerConfig from './component/answer-config.vue';
import DimensionConfigTable from './component/dimension-config-table.vue';
import Caa from './component/caa.vue';
import DiscAssessment from './component/disc-assessment.vue';
import FinancePersonnel from './component/finance-personnel.vue';
import Hots from './component/hots.vue';
import LeadershipSkills from './component/leadership-skills.vue';
import MentalHealthTeamCombination from './component/mental-health-team-combination.vue';
import MotivationAssessment from './component/motivation-assessment.vue';
import ProfessionalWillfulness from './component/professional-willfulness.vue';
import OEA2 from './component/oea2.vue';
import { COLUMNS_FILTER_MAP, COLUMNS } from './constants/constant';
import { useFormDataHooks } from './hooks/useCommonData';
import { NO_COMPUTED_DIMENSION_SCORE } from './constants/validation';
import {
    validateDimensionParticipation,
    validateGeneralDimensionRules,
    validateCoverageDimensions,
    validatePoliticalQualityProduct,
    validateCharacterProductDimensions,
} from './utils/validation';

const $route = useRoute();
const $router = useRouter();
const { formDataBase, tableData, tableColumns, calcTableTotal } = useFormDataHooks();

const mode = computed(() => ($route.query.id ? 'edit' : 'add'));

// 是否展示作答配置
const showAnswerConfig = computed(() => {
    return ![PCode.HOTS, PCode.SRC, PCode.NTA].includes(Number(formDataBase.value.productId));
});
// 是否展示测评维度选择
const showEvaluationDimensionSelect = computed(() => {
    return ![PCode.DISC, PCode.HOTS, PCode.OEA2].includes(Number(formDataBase.value.productId));
});

// 是否展示测评维度表格
const showEvaluationDimensionTable = computed(() => {
    return ![PCode.FCA, PCode.HOTS, PCode.DISC, PCode.MA, PCode.OEA2].includes(Number(formDataBase.value.productId)) && tableData.value.length;
});

// 金融人才测评产品
const isFinancePersonnelProduct = computed(() => {
    return [PCode.FCA].includes(Number(formDataBase.value.productId));
});
// 职业动机测评产品
const isMotivationAssessmentProduct = computed(() => {
    return [PCode.MA].includes(Number(formDataBase.value.productId));
});
// 高阶思维能力测评产品
const isHotsProduct = computed(() => {
    return [PCode.HOTS].includes(Number(formDataBase.value.productId));
});
// 高潜人才识别测评（HIPO）产品
const isHipoProduct = computed(() => {
    return [PCode.HIPO].includes(Number(formDataBase.value.productId));
});
// 敬业度调研测评产品
const isDedicationResearchProduct = computed(() => {
    return [PCode.ES].includes(Number(formDataBase.value.productId));
});
// 领导力情景测评产品
const isLeadershipSkillsProduct = computed(() => {
    return [PCode.LPA].includes(Number(formDataBase.value.productId));
});
// 职业心理健康测评
const isMentalHealthTeam = computed(() => {
    return [PCode.OMHA].includes(Number(formDataBase.value.productId));
});

// 校招方案组合
const isMentalHealthTeamCombination = computed(() => {
    return [PCode.SRC].includes(Number(formDataBase.value.productId));
});

// 新质人才测评方案组合
const isNewTalentCombination = computed(() => {
    return [PCode.NTA].includes(Number(formDataBase.value.productId));
});
// 16T职业性格测评产品
const is16TProfessionalProduct = computed(() => {
    return [PCode.PTA].includes(Number(formDataBase.value.productId));
});
// 五维性格测评产品
const isCharacterProduct = computed(() => {
    return [PCode.OEA, PCode.OEWA].includes(Number(formDataBase.value.productId));
});
// 工作风格测评产品
const isDiscProduct = computed(() => {
    return [PCode.DISC].includes(Number(formDataBase.value.productId));
});
// 笔迹职业适应力测评产品
const isHandwriteProfessionProduct = computed(() => {
    return [PCode.GPA].includes(Number(formDataBase.value.productId));
});
// 认知能力测评
const isCaaProduct = computed(() => {
    return [PCode.CAA].includes(Number(formDataBase.value.productId));
});
// 五维性格测评2.0
const isOEA2Product = computed(() => {
    return [PCode.OEA2].includes(Number(formDataBase.value.productId));
});
// 政治素养测评
const isPoliticalQualityProduct = computed(() => {
    return [PCode.PQA].includes(Number(formDataBase.value.productId));
});
// 职业韧性测评产品、高潜人才识别测评、敬业度调研测评、笔迹职业适应力测评产品、认知能力测评、职业动机测评
const showAdviseAnswerTimeComponent = computed(() => {
    return [PCode.CRA, PCode.HIPO, PCode.ES, PCode.GPA].includes(Number(formDataBase.value.productId));
});
const formRefBase = ref<any>();
const formRefAnswer = ref<any>();
const formRefReport = ref<any>();

const formDataAnswer = ref({
    questionOrderType: 1,
    optionOrderType: 1,
    reviewRuleType: 2,
    timedRuleType: 1,
    reportReferenceOrderType: 1,
    nextQuestion: 2,
});
const formRefDimensionConfigTable = ref<any>();
const dimensionList = ref([]);

const conflictTips = ref([]);
const encryptParentIdSet = ref(new Set());
function changeProduct(value: number) {
    formDataBase.value.dimension = [];
    formDataBase.value.dimensionConfigList = [];
    conflictTips.value = [];
    tableData.value = [];
    formDataBase.value.questionList = [];
    encryptParentIdSet.value = new Set();
    formDataBase.value.questionMethod = 0;
    formDataBase.value.paperTemplateList = []; // 每次切换id的时候 清空子卷列表，避免切换后子卷列表数据混乱
    if ([PCode.HOTS, PCode.FCA, PCode.DISC, PCode.MA, PCode.SRC].includes(value)) {
        getDimensionQuestionDetailInfo(true);
    } else {
        getDimensionList();
        setTableColumns(value);
    }

    // 认知能力产品 默认千人一卷
    if (value === PCode.CAA && formDataBase.value.questionMethod === 0) {
        formDataBase.value.questionMethod = 2;
    }
}

function setTableColumns(value: number) {
    tableColumns.value = COLUMNS.filter((item: any) => !COLUMNS_FILTER_MAP[value]?.includes(item.field));
}
/**
 * 测评模板中已选中的测评维度在返显 和 下方列表展示时，都需要遵循"测评维度统一排序规则"
 * @param selectValue
 * @param existedData
 */
const dimensionFlattenList = ref<any>([]);
function setDimensionData(selectValue: any, existedData?: any) {
    encryptParentIdSet.value = new Set();
    const indexMap: { [key: string]: number } = {};
    dimensionFlattenList.value.forEach((item: { encryptId: string | number; encryptParentId: unknown }, index: number) => {
        indexMap[item.encryptId] = index;
        if ((selectValue?.includes(item.encryptId) || existedData?.some((v: any) => v.encryptDimensionId === item.encryptId)) && item.encryptParentId) {
            encryptParentIdSet.value.add(item.encryptParentId);
        }
    });
    formDataBase.value.dimension = selectValue.length
        ? selectValue?.sort((a: any, b: any) => indexMap[a] - indexMap[b])
        : existedData?.map((item: any) => item.encryptDimensionId) || [];
}
/**
 * @param selectValue select组件change事件参数占位
 * @param existedData 从服务端获取"测评维度"后，生成表格数据时，需要填充的数据
 */
function changeDimension(selectValue: any, existedData?: any) {
    setDimensionData(selectValue, existedData);
    // 获取维度互斥信息
    getConflictDimensionTips();
    tableData.value = formDataBase.value.dimension?.map((dimensionId: any) => {
        const existedDimensionItem = existedData?.find?.((item: any) => item.encryptDimensionId === dimensionId);

        const dimensionItem: any = dimensionFlattenList.value.find((dimensionItem: any) => dimensionItem.encryptId === dimensionId);
        const tableItem = tableData.value.find((tableItem: any) => tableItem.dimensionId === dimensionId);
        if (tableItem) {
            return tableItem;
        } else {
            return {
                dimensionId,
                dimensionName: dimensionItem?.dimensionName,
                inTotal: existedDimensionItem?.participateScore ?? 1,
                weight: existedDimensionItem?.weight ?? undefined,
                questionCount: dimensionItem?.relatedQuestionNum,
                advise: dimensionItem?.adviseAnswerTime,
                limitMin: dimensionItem?.minValidAnswerTime,
                limitMax: dimensionItem?.maxValidAnswerTime,
                isTotal: false,
                rank: existedDimensionItem?.rank,
                showName: existedDimensionItem?.showName || null,
            };
        }
    });
    if (tableData.value?.length > 0) {
        const totalRow = calcTableTotal();
        tableData.value.push(totalRow);
    }
}

function setDimensionConfigList() {
    if (isHotsProduct.value) {
        return [];
    }
    if (isFinancePersonnelProduct.value || isMotivationAssessmentProduct.value || isDiscProduct.value) {
        return formDataBase.value.dimensionConfigList
            .filter((x: any) => x.dimensionId !== -1)
            .map((x: any, i: number) => {
                return {
                    encryptDimensionId: x.encryptDimensionId, // 测评维度加密id
                    participateScore: 1, // 是否参与总分：0-否；1-是
                    questionCount: 0,
                    adviseAnswerTime: 0,
                    minValidAnswerTime: 0, // 有效作答时间最小值
                    maxValidAnswerTime: 0, // 有效作答时间最大值
                    weight: x.weight, // 权重
                    rank: i + 1, // 顺序，从1开始计数
                };
            });
    }
    const data = tableData.value
        .filter((x: any) => x.dimensionId !== -1)
        .map((x: any, i: number) => {
            return {
                encryptDimensionId: x.dimensionId, // 测评维度加密id
                participateScore: x.inTotal, // 是否参与总分：0-否；1-是
                weight: x.weight, // 权重
                questionCount: x.questionCount, // 题目数
                adviseAnswerTime: x.advise, // 建议作答时间
                minValidAnswerTime: x.limitMin, // 有效作答时间最小值
                maxValidAnswerTime: x.limitMax, // 有效作答时间最大值
                rank: x.rank || i + 1, // 顺序，从1开始计数
                showName: x.showName || null,
            };
        });
    return data;
}
const dataChanged = ref(false);
const saved = ref(false);
const loading = ref(false);

async function save() {
    if (loading.value) {
        return;
    }
    const isValid = await validateForm();
    // 不需要限制维度选择个数
    const isDimensionParticipationValid = validateDimensionParticipation(NO_COMPUTED_DIMENSION_SCORE, Number(formDataBase.value.productId), tableData.value);
    if (!isValid || !isDimensionParticipationValid) {
        return;
    }

    const params: any = {
        templateName: formDataBase.value.templateName,
        productId: formDataBase.value.productId,
        instruction: formDataBase.value.instruction,
        dimensionConfigList: setDimensionConfigList(),
        questionList: formDataBase.value.questionList,
        answerConfig: {
            questionOrderType: formDataAnswer.value.questionOrderType, // 题目顺序：1-乱序；2-固定顺序
            optionOrderType: formDataAnswer.value.optionOrderType, // 选项顺序：1-选项乱序（量表题型不乱序）；2-固定顺序
            reviewRuleType: formDataAnswer.value.reviewRuleType, // 回看规则：1-可回看一题）；2-不可回看；3-不限制回看
            reportReferenceOrderType: formDataAnswer.value.reportReferenceOrderType, // 报告参考性题目：1-在所属题型的后50%内随机出现；2-在所属题型内随机出现;当questionOrderType为2时传0
            nextQuestion: formDataAnswer.value.nextQuestion, // 进入下一题：1-选择任一选项后自动进入下一题；2-手动点击【下一题】按钮
            timedRuleType: formDataAnswer.value.timedRuleType, // 是否开启倒计时提醒 1-不开启; 2-开启
        },
    };
    // 组合试卷
    if (isMentalHealthTeamCombination.value || isNewTalentCombination.value) {
        params.paperTemplateList = formDataBase.value.paperTemplateList;
    }

    // 认知能力产品参数
    if (isCaaProduct.value) {
        params.questionMethod = formDataBase.value.questionMethod;
    }

    // 金融人才测评产品、高潜人才识别测评、敬业度调研测评和职业任性测评产品参数
    if (showAdviseAnswerTimeComponent.value || isFinancePersonnelProduct.value || isCaaProduct.value || isMotivationAssessmentProduct.value || isOEA2Product.value) {
        params.adviseAnswerTime = formDataBase.value.adviseAnswerTime;
        params.minValidAnswerTime = formDataBase.value.minValidAnswerTime;
    }

    if (isOEA2Product.value) {
        params.minCompleteQuestionCount = formDataBase.value.minCompleteQuestionCount;
        params.optionDistributionPercentLimit = formDataBase.value.optionDistributionPercentLimit;
    }
    // 领导力情景测评产品、职业心理健康测评产品和16T职业性格测评产品参数
    if (isLeadershipSkillsProduct.value || is16TProfessionalProduct.value || isMentalHealthTeam.value || isDiscProduct.value) {
        params.adviseAnswerTime = formDataBase.value.adviseAnswerTime;
        params.maxValidAnswerTime = formDataBase.value.maxValidAnswerTime;
        params.minValidAnswerTime = formDataBase.value.minValidAnswerTime;
    }
    if (isCaaProduct.value) {
        params.breakTimes = formDataBase.value.breakTimes;
    }

    if (mode.value === 'edit') {
        params.encryptId = $route.query.id;
    }
    try {
        loading.value = true;
        const res = await _saveTemplate(params);
        loading.value = false;
        if (res.code === 0) {
            saved.value = true;
            $router.push({ name: AdminModuleCode.evaluationTemplate.root });
        }
    } catch (error: any) {
        loading.value = false;
        Toast.danger(error.message);
    }
}
function isQualityMonitoringDimension(encryptId: string) {
    // 检查是否为质量监控题：通过判断维度ID是否小于0
    const dimensionId = parseInt(encryptId || '0');
    return dimensionId < 0;
}

async function validateForm() {
    const res = await formRefBase.value.validate();
    const answerRes = await formRefAnswer.value?.validate();
    const reportRes = await formRefReport.value?.validate();
    const errors: any = formValidateSort(res || answerRes || reportRes || {});
    if (errors[Object.keys(errors)[0]]) {
        Toast.danger(errors[Object.keys(errors)[0]]?.message);
        return false;
    } else {
        // 如果是组合试卷，不需要计算维度顺序
        if (isMentalHealthTeamCombination.value || isNewTalentCombination.value) {
            const isEmpty = formDataBase.value.paperTemplateList.filter((item: any) => !item.encryptTemplateId || item.encryptTemplateId === '');
            if (isEmpty.length) {
                Toast.danger('请配置试卷模版');
                return false;
            }

            return true;
        }

        if (isCaaProduct.value) {
            if (!tableData.value?.filter((x: any) => x.dimensionId !== -1).every((x: any) => x.rank || x.rank === 0)) {
                Toast.danger('请输入维度顺序');
                return false;
            }
            const rawData = tableData.value?.filter((x: any) => x.dimensionId !== -1);
            const removeDuplicateData = new Set(rawData.map((item: any) => item.rank));
            if (rawData.length !== removeDuplicateData.size) {
                Toast.danger('顺序不允许重复');
                return false;
            }
        }

        // 金融人才测评产品和职业任性测评产品、认知能力测评需要进行【建议作答时间需要大于等于有效作答时间下限】校验
        if (showAdviseAnswerTimeComponent.value || isFinancePersonnelProduct.value || isCaaProduct.value || isMotivationAssessmentProduct.value || isOEA2Product.value) {
            if (formDataBase.value.adviseAnswerTime < formDataBase.value.minValidAnswerTime) {
                Toast.danger('建议作答时间需要大于等于有效作答时间下限');
                return false;
            }
        }

        if (isFinancePersonnelProduct.value) {
            if (!formDataBase.value.dimensionConfigList?.filter((x: any) => x.dimensionId !== -1).every((x: any) => x.weight || x.weight === 0)) {
                Toast.danger('请输入权重');
                return false;
            }
            return true;
        }

        // 领导力情景测评产品、职业心理健康测评产品和16T职业性格测评产品需要进行【建议作答时间必须在有效作答时间范围内】校验
        if (isLeadershipSkillsProduct.value || is16TProfessionalProduct.value || isMentalHealthTeam.value || isDiscProduct.value) {
            if (formDataBase.value.maxValidAnswerTime <= formDataBase.value.adviseAnswerTime || formDataBase.value.minValidAnswerTime >= formDataBase.value.adviseAnswerTime) {
                Toast.danger('建议作答时间必须在有效作答时间范围内');
                return false;
            }
        }

        // 16T职业性格测评产品测评维度有且只能有4个
        if (is16TProfessionalProduct.value) {
            if (tableData.value.length !== 5) {
                Toast.danger('测评维度有且只能有4个');
                return false;
            }
            return true;
        }

        // 五维性格测评产品测评维度至少选择3个
        if (isCharacterProduct.value) {
            return validateCharacterProductDimensions(tableData.value);
        }

        // 政治素养测评产品
        if (isPoliticalQualityProduct.value) {
            return validatePoliticalQualityProduct(tableData.value, dimensionFlattenList.value);
        }

        if (isHandwriteProfessionProduct.value) {
            return validateCoverageDimensions(isHandwriteProfessionProduct.value, dimensionList.value, formDataBase.value, encryptParentIdSet.value);
        }

        if (isHipoProduct.value || isDedicationResearchProduct.value) {
            return validateCoverageDimensions(undefined, dimensionList.value, formDataBase.value, encryptParentIdSet.value);
        }
        // 通用维度规则校验
        return validateGeneralDimensionRules(formRefDimensionConfigTable.value, tableData.value, tableColumns.value);
    }
}
function cancel() {
    $router.push({ name: AdminModuleCode.evaluationTemplate.root });
}
onBeforeRouteLeave((to, from, next) => {
    // 只这样写，会导致：在点击页面左上角返回、或浏览器自带返回按钮时，页面地址先变化，然后再弹窗。假如此时刷新页面……所以必须配合window.beforeunload，适时阻止页面刷新。
    if (saved.value) {
        // if (mode.value === 'add' && to.path === AdminModuleCode.evaluationTemplate.root) {
        if (to.path === AdminModuleCode.evaluationTemplate.root) {
            to.query.needUpdate = '1';
            if (mode.value === 'add') {
                to.query.resetPage = '1';
            }
        }
        next();
    } else {
        if (dataChanged.value) {
            Dialog.open({
                type: 'warning',
                title: '离开确认',
                content: '当前数据尚未保存，确定要离开吗？',
                confirm() {
                    next();
                },
                cancel() {
                    next(false);
                },
            });
        } else {
            next();
        }
    }
});

async function getDimensionList(needContainsQuestion?: boolean, exceptEncryptId?: string) {
    const params = {
        dimensionStatus: 0,
        productId: formDataBase.value.productId,
        needContainsQuestion,
        exceptEncryptId,
    };
    const res = await _getDimensionDetailInfo(params);
    if (res.code === 0) {
        const initAllDimension = [PCode.OEA, PCode.OEWA, PCode.LPA, PCode.HIPO, PCode.ES, PCode.CAA, PCode.GPA, PCode.PQA].includes(Number(formDataBase.value.productId));
        dimensionFlattenList.value = res.data.filter((i: any) => i.relatedQuestionNum);
        dimensionList.value = res.data
            .filter((i: any) => i.relatedQuestionNum)
            ?.map((x: any) => {
                if (initAllDimension && mode.value === 'add' && !x.children?.length) {
                    formDataBase.value.dimension.push(x.encryptId);
                }
                return {
                    ...x,
                    children: x.children
                        ?.filter((i: any) => i.relatedQuestionNum)
                        .map((x: any) => {
                            if (initAllDimension && mode.value === 'add') {
                                formDataBase.value.dimension.push(x.encryptId);
                            }
                            dimensionFlattenList.value.push(x);
                            return x;
                        }),
                };
            });

        if (initAllDimension && mode.value === 'add') {
            await nextTick(() => {
                changeDimension(formDataBase.value.dimension);
            });
        }
    }
}

async function getConflictDimensionTips() {
    if (formDataBase.value.dimension?.length >= 2) {
        const params = formDataBase.value.dimension;
        const res = await _getConflictDimensionTips(params);
        if (res.code === 0) {
            conflictTips.value = res.data || [];
        }
    } else {
        conflictTips.value = [];
    }
}

/**
 * 可选测评试题（包含测评试题详细信息）
 */
const questionDetailInfoList = ref([]);
async function getDimensionQuestionDetailInfo(isAllChecked = false) {
    try {
        const { code, data } = await _getDimensionOptionContainsDetailInfo({
            productId: formDataBase.value.productId,
            questionStatus: 0, // 0-启用；1-禁用
        });
        if (code === 0) {
            questionDetailInfoList.value = data;
            if (isAllChecked) {
                formDataBase.value.questionList = data?.map((item: { id: any }) => item.id);
            }
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    }
}
const detailData = ref();
// 不能一进页面就绑定刷新事件，因为内容有可能没有改变。
onMounted(async () => {
    //     window.onbeforeunload = function () {
    //         return '确认离开此页面吗？';
    //     };

    if (mode.value === 'add') {
        await getDimensionList();
    }
    if (mode.value === 'edit') {
        await getDetail();
        if (detailData.value) {
            formDataBase.value.paperTemplateList = detailData.value?.paperTemplateList || []; // 组合试卷赋值
            formDataBase.value.dimensionConfigList = detailData.value.dimensionConfigList || [];
            formDataBase.value.templateName = detailData.value.templateName;
            formDataBase.value.productId = detailData.value.productId;
            formDataBase.value.productName = detailData.value.productName;
            formDataBase.value.adviseAnswerTime = detailData.value.adviseAnswerTime;
            formDataBase.value.minValidAnswerTime = detailData.value.minValidAnswerTime;
            formDataBase.value.maxValidAnswerTime = detailData.value.maxValidAnswerTime;
            formDataBase.value.minCompleteQuestionCount = detailData.value.minCompleteQuestionCount;
            formDataBase.value.optionDistributionPercentLimit = detailData.value.optionDistributionPercentLimit;
            formDataBase.value.questionList = detailData.value.questionList;
            formDataBase.value.breakTimes = detailData.value.breakTimes;
            formDataBase.value.questionMethod = detailData.value.questionMethod;

            if (isFinancePersonnelProduct.value || isHotsProduct.value || isMotivationAssessmentProduct.value || isDiscProduct.value || isOEA2Product.value) {
                await getDimensionQuestionDetailInfo();
            } else {
                await getDimensionList();
            }
            formDataBase.value.instruction = detailData.value.instruction;
            formDataBase.value.dimension = detailData.value.dimensionConfigList?.filter((i: any) => i.questionCount).map((x: any) => x.encryptDimensionId);

            changeDimension([], detailData.value.dimensionConfigList);

            formDataAnswer.value.questionOrderType = detailData.value.answerConfig.questionOrderType;
            formDataAnswer.value.optionOrderType = detailData.value.answerConfig.optionOrderType;
            formDataAnswer.value.reviewRuleType = detailData.value.answerConfig.reviewRuleType;
            formDataAnswer.value.timedRuleType = detailData.value.answerConfig.timedRuleType;
            formDataAnswer.value.nextQuestion = detailData.value.answerConfig.nextQuestion || 2;
            formDataAnswer.value.reportReferenceOrderType = formDataAnswer.value.questionOrderType === 2 ? 0 : detailData.value.answerConfig.reportReferenceOrderType || 1;
        }
    }
    dataSnapshot.value.formDataBase = cloneDeep(formDataBase.value);
    dataSnapshot.value.formDataAnswer = cloneDeep(formDataAnswer.value);
    dataSnapshot.value.tableData = cloneDeep(tableData.value);
    startWatchData();
});
// 金融人才测评初始化详情页更新快照数据
function updateDataSnapshot() {
    dataSnapshot.value.formDataBase = cloneDeep(formDataBase.value);
}
onUnmounted(() => {
    window.onbeforeunload = null;
});

function startWatchData() {
    watch(
        [() => formDataBase.value, () => formDataAnswer.value, () => tableData.value],
        () => {
            const isBaseEqual = isEqual(formDataBase.value, dataSnapshot.value.formDataBase);
            const isAnswerEqual = isEqual(formDataAnswer.value, dataSnapshot.value.formDataAnswer);
            const isTableEqual = isEqual(tableData.value, dataSnapshot.value.tableData);
            dataChanged.value = [isBaseEqual, isAnswerEqual, isTableEqual].includes(false);
            if (dataChanged.value) {
                window.onbeforeunload = function () {
                    return '确认离开此页面吗？';
                };
            } else {
                window.onbeforeunload = null;
            }
        },
        { deep: true }
    );
}

const dataSnapshot = ref({
    formDataBase: {},
    formDataAnswer: {},
    tableData: [],
});

const showLoading = ref<boolean>(false);
async function getDetail() {
    const params = {
        encryptId: $route.query.id,
    };
    try {
        showLoading.value = true;
        const res = await _getTemplateInfo(params);
        if (res.code === 0) {
            detailData.value = res.data;
            setTableColumns(res.data.productId);
        }
    } catch (error: any) {
        Toast.danger(error);
    } finally {
        showLoading.value = false;
    }
}
</script>

<style lang="less" scoped>
@import '@/styles/evaluation/template/detail.less';
</style>

<style lang="less" scoped>
:global(.b-cascader-option-label) {
    width: 296px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
