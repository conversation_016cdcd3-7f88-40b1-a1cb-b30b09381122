import { createSharedComposable } from '@crm/vueuse-pro';
import { ref } from 'vue';
import { add } from '@/utils/math';
import { COLUMNS, COLUMNS_FILTER_MAP } from '../constants/constant';

function useFormData() {
    const formDataBase = ref<any>({
        templateName: '',
        productId: '',
        instruction: '',
        dimension: [],
        productName: '',
        adviseAnswerTime: undefined,
        minValidAnswerTime: undefined,
        maxValidAnswerTime: undefined,
        questionList: [],
        dimensionConfigList: [],
        breakTimes: undefined,
        paperTemplateList: [], // 组合方案-子卷模板列表
        questionMethod: 0, // 2-随机出题（千人一卷）3 - 随机出题（千人千卷）-（认知能力产品）
        minCompleteQuestionCount: undefined,
        optionDistributionPercentLimit: undefined,
    });

    const tableData = ref<any>([]);

    const tableColumns = ref<any>(COLUMNS);

    function calcTableTotal() {
        const totalRow = {
            dimensionId: -1,
            dimensionName: '总计',
            inTotal: '',
            weight: 0,
            questionCount: 0,
            advise: 0,
            limitMin: 0,
            limitMax: 0,
            isTotal: !COLUMNS_FILTER_MAP[formDataBase.value.productId]?.includes('inTotal'),
        };
        for (let i = 0; i < tableData.value.length; i++) {
            const tableItem = tableData.value[i];
            if (tableItem.dimensionId !== -1) {
                totalRow.weight = add(totalRow.weight ?? 0, tableItem.weight ?? 0);
                totalRow.questionCount += tableItem.questionCount;
                totalRow.advise = add(totalRow.advise, tableItem.advise);
                totalRow.limitMin = add(totalRow.limitMin, tableItem.limitMin);
                totalRow.limitMax = add(totalRow.limitMax, tableItem.limitMax);
            }
        }
        return totalRow;
    }
    return {
        formDataBase,
        tableData,
        tableColumns,
        calcTableTotal,
    };
}

export const useFormDataHooks = createSharedComposable(useFormData);
