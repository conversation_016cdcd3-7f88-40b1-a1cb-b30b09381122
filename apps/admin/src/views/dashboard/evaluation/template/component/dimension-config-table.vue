<template>
    <b-table class="dimension-table" :tableData="tableData" :columns="tableColumns" :tdProps="tdProps">
        <template #th-rank> 维度顺序<span style="color: var(--danger-color-6); margin-left: 4px">*</span> </template>
        <template #th-showName> 维度展示名称<span style="color: var(--danger-color-6); margin-left: 4px">*</span> </template>
        <template #th-weight> 权重<span style="color: var(--danger-color-6); margin-left: 4px">*</span> </template>
        <template #td-inTotal="{ raw }">
            <b-switch v-model="raw.inTotal" :checkedValue="1" :uncheckedValue="0" @change="(value: number) => changeInTotal(value, raw)" />
        </template>
        <template #td-rank="{ raw }">
            <template v-if="raw.dimensionName === '总计'">
                <span />
            </template>
            <b-input-number v-else v-model="raw.rank" size="small" :min="1" :max="100" hideButton :precision="0" placeholder="请输入" />
        </template>
        <template #td-showName="{ raw }">
            <template v-if="raw.dimensionName === '总计'">
                <span />
            </template>
            <b-input v-else v-model="raw.showName" size="small" hideButton :precision="0" placeholder="请输入" />
        </template>
        <template #td-weight="{ raw }">
            <div v-if="raw.isTotal || (isCaaProduct && raw.dimensionName === '总计')">{{ raw.weight }}%</div>
            <b-input-number
                v-else-if="raw.inTotal === 1"
                v-model="raw.weight"
                size="small"
                :min="1"
                :max="100"
                :step="1"
                :precision="0"
                placeholder="请输入"
                @change="changeWeight"
            >
                <template #append> % </template>
            </b-input-number>
            <template v-else> - </template>
        </template>
        <template #td-advise="{ raw }"> {{ raw.advise }}分钟 </template>
        <template #td-limit="{ raw }"> {{ raw.limitMin }} - {{ raw.limitMax }}分钟 </template>
        <template #td-minLimit="{ raw }"> {{ raw.limitMin }}分钟 </template>
    </b-table>
</template>

<script setup lang="ts" name="DimensionConfigTable">
import type { ITableData } from '@boss/design';
import { computed } from 'vue';
import { PCode } from '@crm/biz-exam-product';
import { useFormDataHooks } from '../hooks/useCommonData';

const { formDataBase, tableData, tableColumns, calcTableTotal } = useFormDataHooks();

// 认知能力测评
const isCaaProduct = computed(() => {
    return [PCode.CAA].includes(Number(formDataBase.value.productId));
});

function tdProps(row: ITableData, col: any): any {
    if (row.raw.isTotal && col.raw.field === 'dimensionName') {
        return {
            colSpan: 2,
        };
    }
    if (row.raw.isTotal && col.raw.field === 'inTotal') {
        return {
            colSpan: 0,
        };
    }
    return {
        colSpan: 1,
    };
}

function changeWeight() {
    const totalRow = calcTableTotal();
    tableData.value.splice(tableData.value.length - 1, 1, totalRow);
}

function changeInTotal(value: number, raw: any) {
    if (value === 0) {
        raw.weight = undefined;
    }
    const totalRow = calcTableTotal();
    tableData.value.splice(tableData.value.length - 1, 1, totalRow);
}
</script>

<style scoped lang="scss"></style>
