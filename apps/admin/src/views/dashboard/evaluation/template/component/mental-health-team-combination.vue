<template>
    <b-table v-model:tableData="paperTemplateList" rowKey="encryptProductId" :columns="columns" :treeMode="{ defaultExpandAll: true }">
        <template #td-encryptProductId="{ raw, $index }">
            <CombinationItemSelect v-model:value="paperTemplateList[$index]" :encryptId="raw.encryptProductId" :productId="raw.productId" />
        </template>
    </b-table>
</template>

<script setup lang="ts">
import type { ITableColumnDataRaw } from '@boss/design';
import { watch } from 'vue';
import CombinationItemSelect from './combination-item-select.vue';

// 表单的值
const paperTemplateList = defineModel<TTemplateItem[]>('value', {
    default: [],
});

const props = defineProps({
    productId: {
        type: Number,
        default: 0,
    },
});

interface TTemplateItem {
    productId: number;
    encryptProductId: string;
    productName: string;
    templateId: number;
    encryptTemplateId: string;
    templateName: string;
    rank: number;
}

// 拖拽结束回调
function onDragend() {
    paperTemplateList.value.forEach((item, index) => {
        item.rank = index + 1;
    });
}

const columns: Partial<ITableColumnDataRaw>[] = [
    {
        type: 'drag',
        drag: {
            type: 'handle',
            sourceProps: {
                onDragend,
            },
        },
        width: 40,
    },
    {
        label: '产品',
        field: 'productName',
        width: 180,
    },
    {
        label: '模板',
        field: 'encryptProductId',
    },
    {
        label: '排序',
        field: 'rank',
        width: 100,
    },
];

async function getTableData(query: any) {
    const { code, data } = await Invoke.evaluationTemplate.getProductOptions({ ...query });
    if (code === 0 && data && data.length) {
        // 如果value为空，需要预设置子卷产品类型
        if (!paperTemplateList.value.length) {
            paperTemplateList.value = data.map((item: any, index: number) => {
                return {
                    productId: item.id,
                    encryptProductId: item.encryptId,
                    productName: item.name,
                    templateId: 0,
                    encryptTemplateId: '',
                    templateName: '',
                    rank: index + 1,
                };
            });
        }
    }
}

watch(
    () => props.productId,
    (productId) => {
        if (productId && paperTemplateList.value.length === 0) {
            getTableData({ productId });
        }
    },
    {
        immediate: true,
    }
);
</script>
