<template>
    <b-form-item label="模板名称" required asteriskPosition="end" field="templateName" :rules="[{ type: 'string', required: true, message: '请输入模板名称' }]">
        <b-input v-model.trim="formDataBase.templateName" trimBeforePaste placeholder="请输入" :maxLength="30" allowClear showWordLimit />
    </b-form-item>
    <b-form-item label="测评产品" required asteriskPosition="end" field="productId" :rules="[{ type: 'number', required: true, message: '请选择测评产品' }]">
        <b-select v-if="mode === 'add'" v-model="formDataBase.productId" placeholder="请选择" @change="emit('changeProduct', $event)">
            <b-option v-for="item of productList" :key="item.id" :value="item.id">
                {{ item.name }}
            </b-option>
        </b-select>
        <div v-else class="product-name">
            {{ formDataBase.productName || '--' }}
        </div>
    </b-form-item>
    <b-form-item v-if="showInstruction" label="指导语" required asteriskPosition="end" field="instruction" :rules="[{ type: 'string', required: true, message: '请输入指导语' }]">
        <b-textarea v-model.trim="formDataBase.instruction" placeholder="考生在答题时可见的说明" :maxLength="200" showWordLimit :autoSize="{ minRows: 4, maxRows: 9 }" />
    </b-form-item>
</template>

<script setup lang="ts" name="BaseInfoForm">
import { computed, ref } from 'vue';
import { PCode } from '@crm/biz-exam-product';
import { useFormDataHooks } from '../hooks/useCommonData';
import { _getProductOptions } from '@/services/api/project';

const props = defineProps({
    mode: {
        type: String,
        default: 'add',
    },
});

const emit = defineEmits(['changeProduct']);

const { formDataBase } = useFormDataHooks();

// 是否展示指导语
const showInstruction = computed(() => {
    let isShow = true; // 默认展示
    if ([PCode.SRC, PCode.NTA, PCode.HOTS].includes(Number(formDataBase.value.productId))) {
        isShow = false;
    }
    return isShow;
});

const productList = ref<any>([]);

async function getProductList() {
    const res = await _getProductOptions({
        source: 1, // source=1 表示可以返回 校招组合 测评产品
    });
    if (res.code === 0) {
        productList.value = res.data?.filter((item: { id: number }) => ![PCode.GBA].includes(item.id)) || [];
    }
}

if (props.mode === 'add') {
    getProductList();
}
</script>

<style scoped lang="scss"></style>
