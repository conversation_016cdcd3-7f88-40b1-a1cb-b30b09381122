<template>
    <b-row :gutter="[20, 24]" class="professional-willfulness-form-item-wrap">
        <b-col :span="12">
            <b-form-item label="建议作答时间" required asteriskPosition="end" field="adviseAnswerTime" :rules="[{ type: 'number', required: true, message: '请输入建议作答时间' }]">
                <b-input-number
                    v-model="modelValue.adviseAnswerTime"
                    class="input-number-block-suffix"
                    placeholder="请填写200以内的数字，最多1位小数"
                    hideButton
                    :min="0.1"
                    :max="200"
                    :precision="1"
                >
                    <template #suffix> 分钟 </template>
                </b-input-number>
            </b-form-item>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">考生答题时可见的提示时间</span>
            </div>
        </b-col>
        <b-col :span="12">
            <b-form-item
                label="有效作答时间下限"
                required
                asteriskPosition="end"
                field="minValidAnswerTime"
                :rules="[{ type: 'number', required: true, message: '请输入有效作答时间下限' }]"
            >
                <b-input-number
                    v-model="modelValue.minValidAnswerTime"
                    class="input-number-block-suffix"
                    placeholder="请填写30以内的数字，最多1位小数"
                    hideButton
                    :min="0.1"
                    :max="30"
                    :precision="1"
                >
                    <template #suffix> 分钟 </template>
                </b-input-number>
            </b-form-item>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">报告参考性的判断条件</span>
            </div>
        </b-col>
        <b-col :span="12">
            <b-form-item label="作答完成情况" required asteriskPosition="end" field="adviseAnswerTime" :rules="[{ type: 'number', required: true, message: '请输入建议作答时间' }]">
                <b-input-number
                    v-model="modelValue.minCompleteQuestionCount"
                    class="input-number-block-suffix"
                    placeholder="请输入最少完成题目百分比"
                    hideButton
                    :min="0"
                    :max="100"
                    :precision="1"
                >
                    <template #suffix> % </template>
                </b-input-number>
            </b-form-item>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">小于X，则报告中本项异常，范围0～100</span>
            </div>
        </b-col>
        <b-col :span="12">
            <b-form-item
                label="选项分布"
                required
                asteriskPosition="end"
                field="minValidAnswerTime"
                :rules="[{ type: 'number', required: true, message: '请输入有效作答时间下限' }]"
            >
                <b-input-number
                    v-model="modelValue.optionDistributionPercentLimit"
                    class="input-number-block-suffix"
                    placeholder="请输入选项分布限制百分比"
                    hideButton
                    :min="0"
                    :max="100"
                    :precision="0"
                >
                    <template #suffix> % </template>
                </b-input-number>
            </b-form-item>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">大于 X，则报告中本项异常，范围0～100</span>
            </div>
        </b-col>
        <b-col :span="24">
            <b-form-item
                label="测评题目"
                required
                class="b-form-item-wrap"
                asteriskPosition="end"
                field="questionList"
                :rules="[{ type: 'array', required: true, message: '请选择测评题目' }]"
            >
                <b-select
                    v-model="modelValue.questionList"
                    placeholder="请选择"
                    multiple
                    collapseTagsTooltip
                    :maxTagCount="1"
                    class="dimension-question"
                    :tooltipProps="tooltipProps"
                    allowClear
                    @change="handleChangeQuestion"
                >
                    <b-option v-for="item of questionDetailInfoList" :key="item.id" :value="item.id">
                        {{ `${item.id} | ${item.questionTitle}` }}
                    </b-option>
                </b-select>
            </b-form-item>
        </b-col>
    </b-row>
</template>

<script setup lang="ts" name="OEA2">
const props = defineProps({
    modelValue: {
        type: Object,
        default: () => {},
    },
    questionDetailInfoList: {
        type: Array,
        default: () => [],
    },
});

const tooltipProps = {
    contentStyle: {
        maxHeight: '500px',
        overflowY: 'auto',
    },
};

function handleChangeQuestion(val: any) {
    if (val?.length && props.mode === 'edit') {
        // getDimensionInfos(val);
    }
    if (!val?.length) {
        props.modelValue.dimensionConfigList = [];
    }
}
</script>

<style lang="less" scoped>
@import '@/styles/evaluation/template/detail.less';
</style>
