<template>
    <b-form ref="formRef" class="form-answer-config" :model="formData" gentleValidation labelAlign="left">
        <b-form-item
            label="题目顺序"
            required
            asteriskPosition="end"
            field="questionOrderType"
            :rules="[{ type: 'number', required: true, message: '请选择题目顺序' }]"
            @change="handleChangeQuestionOrder"
        >
            <b-radio-group v-model="formData.questionOrderType">
                <b-radio :value="1" :ripple="false"> 同题型内乱序 </b-radio>
                <b-radio :value="2" :ripple="false"> 固定顺序 </b-radio>
            </b-radio-group>
        </b-form-item>
        <b-form-item
            v-if="formData.questionOrderType === 1 && showOptionOrderType"
            label="报告参考性题目"
            required
            asteriskPosition="end"
            field="optionOrderType"
            :rules="[{ type: 'number', required: true, message: '请选择报告参考性题目' }]"
        >
            <b-radio-group v-model="formData.reportReferenceOrderType">
                <b-radio :value="1" :ripple="false"> 在所属题型的后50%内随机出现 </b-radio>
                <b-radio :value="2" :ripple="false"> 在所属题型内随机出现 </b-radio>
            </b-radio-group>
        </b-form-item>
        <b-form-item label="选项顺序" required asteriskPosition="end" field="optionOrderType" :rules="[{ type: 'number', required: true, message: '请选择选项顺序' }]">
            <b-radio-group v-model="formData.optionOrderType">
                <b-radio :value="1" :ripple="false"> 选项乱序（量表题型不乱序） </b-radio>
                <b-radio :value="2" :ripple="false"> 固定顺序 </b-radio>
            </b-radio-group>
        </b-form-item>
        <b-form-item label="进入下一题" required asteriskPosition="end" field="nextQuestion" :rules="[{ type: 'number', required: true, message: '请选择进入下一题' }]">
            <b-radio-group v-model="formData.nextQuestion" :disabled="nextQuestionDisabled">
                <b-radio :value="1" :ripple="false"> 选择任一选项后自动进入下一题 </b-radio>
                <b-radio :value="2" :ripple="false"> 手动点击【下一题】按钮 </b-radio>
            </b-radio-group>
        </b-form-item>
        <b-form-item
            v-if="showReviewRuleType"
            label="回看规则"
            required
            asteriskPosition="end"
            field="reviewRuleType"
            :rules="[{ type: 'number', required: true, message: '请选择回看规则' }]"
        >
            <b-radio-group v-model="formData.reviewRuleType">
                <b-radio :value="1" :ripple="false"> 可回看一题 </b-radio>
                <b-radio :value="2" :ripple="false"> 不可回看 </b-radio>
                <b-radio :value="3" :ripple="false"> 不限制回看 </b-radio>
            </b-radio-group>
        </b-form-item>
        <b-form-item label="倒计时提醒" required asteriskPosition="end" field="timedRuleType" :rules="[{ type: 'number', required: true, message: '请选择是否开启倒计时提醒' }]">
            <b-radio-group v-model="formData.timedRuleType" :disabled="timedRuleTypeDisabled">
                <b-radio :value="1" :ripple="false"> 不开启 </b-radio>
                <b-radio :value="2" :ripple="false"> 开启 </b-radio>
            </b-radio-group>
        </b-form-item>
    </b-form>
</template>

<script setup lang="ts">
import { PCode } from '@crm/biz-exam-product';
import { computed, ref, watch } from 'vue';

const formData = defineModel('value', {
    default: {
        questionOrderType: 1,
        optionOrderType: 1,
        reviewRuleType: 2,
        timedRuleType: 1,
        reportReferenceOrderType: 1,
        nextQuestion: 2,
    },
});
const props = defineProps({
    productId: {
        // 产品id
        type: Number,
        default: 0,
    },
});
const formRef = ref<any>();

// 下一步选项是否禁用
const nextQuestionDisabled = computed(() => {
    const flag = [PCode.ES, PCode.HIPO, PCode.CAA, PCode.FCA, PCode.DISC, PCode.MA].includes(props.productId);
    return flag;
});
// 倒计时提醒是否禁用
const timedRuleTypeDisabled = computed(() => {
    const flag = [PCode.CAA].includes(props.productId);
    return flag;
});

// 是否显示选项顺序配置
const showOptionOrderType = computed(() => {
    const flag = [PCode.FCA, PCode.LPA, PCode.HIPO].includes(props.productId);
    return flag;
});

// 是否显示回看规则配置
const showReviewRuleType = computed(() => {
    const flag = PCode.CAA !== props.productId;
    return flag;
});

// 初始化表单数据
function initFormData() {
    return {
        questionOrderType: 1,
        optionOrderType: 1,
        reviewRuleType: 2,
        timedRuleType: 1,
        reportReferenceOrderType: 1,
        nextQuestion: 2,
    };
}

function handleChangeQuestionOrder(e: any) {
    formData.value.reportReferenceOrderType = Number(e.target.value) === 2 ? 0 : 1;
}

// 根据产品类型调整配置
watch(
    () => props.productId,
    (productId) => {
        const data = { ...initFormData() };

        // 金融Junior人才胜任力、高潜人才识别测评、工作风格检验测评仅支持手动点击【下一题】按钮，默认选中，不可切换（原因：迫选题仅支持手动下一题）
        if ([PCode.ES, PCode.HIPO, PCode.CAA, PCode.FCA, PCode.DISC, PCode.MA].includes(productId)) {
            data.nextQuestion = 2;
        }

        if ([PCode.HOTS].includes(productId)) {
            data.optionOrderType = 2;
        }

        if ([PCode.HIPO, PCode.CAA].includes(productId)) {
            data.reviewRuleType = 2;
        }

        if ([PCode.CAA].includes(productId)) {
            data.timedRuleType = 2;
        }

        formData.value = data;
    },
    { immediate: true }
);

defineExpose({
    validate: () => {
        return formRef.value.validate();
    },
});
</script>

<style lang="less" scoped>
:deep(.form-answer-config) {
    .b-form-item-label {
        line-height: 22px;
    }

    .b-form-item-wrapper-col {
        min-height: 22px;

        .b-form-item-content {
            min-height: 22px;
        }

        .b-radio-group {
            display: inline-flex;
            flex-wrap: wrap;

            .b-radio {
                line-height: 22px;
            }
        }
    }
}
</style>
