import { VALIDATION_MESSAGES, DIMENSION_VALIDATION_RULES } from '../constants/validation';

// 验证工具函数
export function isQualityMonitoringDimension(encryptId: string): boolean {
    const dimensionId = parseInt(encryptId || '0', 10);
    return dimensionId < 0;
}

// 验证组合试卷
export function validateCombinationPaper(isMentalHealthTeamCombination: boolean, isNewTalentCombination: boolean, paperTemplateList: any[]): boolean {
    if (!isMentalHealthTeamCombination && !isNewTalentCombination) {
        return true;
    }

    const isEmpty = paperTemplateList.filter((item: any) => !item.encryptTemplateId || item.encryptTemplateId === '');

    if (isEmpty.length) {
        Toast.danger(VALIDATION_MESSAGES.COMBINATION_PAPER);
        return false;
    }

    return true;
}

// 验证认知能力产品
export function validateCaaProduct(isCaaProduct: boolean, tableData: any[]): boolean {
    if (!isCaaProduct) {
        return true;
    }

    const validDimensions = tableData?.filter((x: any) => x.dimensionId !== -1);
    const hasValidRanks = validDimensions.every((x: any) => x.rank || x.rank === 0);

    if (!hasValidRanks) {
        Toast.danger(VALIDATION_MESSAGES.DIMENSION_ORDER);
        return false;
    }

    const ranks = validDimensions.map((item: any) => item.rank);
    const uniqueRanks = new Set(ranks);

    if (ranks.length !== uniqueRanks.size) {
        Toast.danger(VALIDATION_MESSAGES.DUPLICATE_ORDER);
        return false;
    }

    return true;
}

// 验证时间配置
export function validateTimeConfiguration(needsTimeValidation: boolean, adviseAnswerTime: number, minValidAnswerTime: number): boolean {
    if (!needsTimeValidation) {
        return true;
    }

    if (adviseAnswerTime < minValidAnswerTime) {
        Toast.danger(VALIDATION_MESSAGES.TIME_RANGE);
        return false;
    }

    return true;
}

// 验证金融人才产品权重
export function validateFinancePersonnelWeights(isFinancePersonnelProduct: boolean, dimensionConfigList: any[]): boolean {
    if (!isFinancePersonnelProduct) {
        return true;
    }

    const hasValidWeights = dimensionConfigList?.filter((x: any) => x.dimensionId !== -1).every((x: any) => x.weight || x.weight === 0);

    if (!hasValidWeights) {
        Toast.danger(VALIDATION_MESSAGES.WEIGHT_REQUIRED);
        return false;
    }

    return true;
}

// 验证领导力产品时间范围
export function validateLeadershipTimeRange(needsTimeRangeValidation: boolean, maxValidAnswerTime: number, minValidAnswerTime: number, adviseAnswerTime: number): boolean {
    if (!needsTimeRangeValidation) {
        return true;
    }

    if (maxValidAnswerTime <= adviseAnswerTime || minValidAnswerTime >= adviseAnswerTime) {
        Toast.danger(VALIDATION_MESSAGES.LEADERSHIP_TIME_RANGE);
        return false;
    }

    return true;
}

// 验证16T产品维度数量
export function validate16TProductDimensions(is16TProfessionalProduct: boolean, tableData: any[]): boolean {
    if (!is16TProfessionalProduct) {
        return true;
    }

    if (tableData.length !== 5) {
        Toast.danger(VALIDATION_MESSAGES.DIMENSION_COUNT_16T);
        return false;
    }

    return true;
}

// 验证性格产品维度数量
export function validateCharacterProductDimensions(tableData: any[]): boolean {
    if (tableData.length <= 3) {
        Toast.danger('至少选择3个维度');
        return false;
    }
    return true;
}

// 验证政治素养产品
export function validatePoliticalQualityProduct(tableData: any[], dimensionFlattenList: any[]): boolean {
    const selectedDimensionsWithDetails = tableData
        .filter((x: any) => x.dimensionId !== -1 && !x.isTotal)
        .map((item: any) => {
            return dimensionFlattenList.find((d: any) => d.encryptId === item.dimensionId);
        })
        .filter(Boolean);

    const nonQualityMonitoringDimensions = selectedDimensionsWithDetails.filter((d: any) => !isQualityMonitoringDimension(d.id));

    const secondLevelDimensions = nonQualityMonitoringDimensions.filter((d: any) => d.encryptParentId);

    const parentIdSet = new Set<string>();
    secondLevelDimensions.forEach((item: any) => {
        parentIdSet.add(item.encryptParentId);
    });

    if (secondLevelDimensions.length < 4 || parentIdSet.size < 2) {
        Toast.danger('最少选中4个二级维度，2个一级维度（不含质量监控维度）');
        return false;
    }
    if (secondLevelDimensions.length > 20 || parentIdSet.size > 8) {
        Toast.danger('最多包含20个二级维度，8个一级维度（不含质量监控维度）');
        return false;
    }
    return true;
}

// 验证覆盖维度
export function validateCoverageDimensions(isHandwriteProfessionProduct?: boolean, dimensionList?: any[], formDataBase?: any, encryptParentIdSet?: Set<any>): boolean {
    const nameList: string[] = [];

    if (isHandwriteProfessionProduct) {
        dimensionList
            ?.filter((item: { encryptId: string }) => !formDataBase.dimension.includes(item.encryptId))
            .forEach((item: { dimensionName: string }) => {
                nameList.push(`【${item.dimensionName}】`);
            });
    } else {
        dimensionList?.forEach((item: { encryptId: string; dimensionName: string }) => {
            if (!encryptParentIdSet?.has(item.encryptId)) {
                nameList.push(`【${item.dimensionName}】`);
            }
        });
    }

    if (nameList.length) {
        const errorText = isHandwriteProfessionProduct
            ? `已选一级维度需要覆盖本产品${dimensionList?.length}个一级维度，当前未覆盖${nameList.join('、')}，请修改`
            : `已选二级维度所属的一级维度需要覆盖本产品${dimensionList?.length}个一级维度，当前未覆盖${nameList.join('、')}，请修改`;
        Toast.danger(errorText);
    }

    return !nameList.length;
}

// 验证通用维度规则
export function validateGeneralDimensionRules(formRefDimensionConfigTable: any, tableData: any[], tableColumns: any[]): boolean {
    if (!formRefDimensionConfigTable) {
        return true;
    }
    // const validCodes = [PCode.HMA, PCode.OEA, PCode.CRA, PCode.LPA, PCode.OEWA, PCode.PTA, PCode.OMHA, PCode.MA];
    // 检查是否有参与总分的维度
    const hasParticipatingDimensions = tableData.filter((x: any) => x.dimensionId !== -1).some((x: any) => x.inTotal === 1);

    if (!hasParticipatingDimensions) {
        Toast.danger('至少有一个参与总分的维度');
        return false;
    }

    // 检查权重输入
    const needsWeightValidation = tableColumns.some((x: any) => x.field === 'weight');
    if (needsWeightValidation) {
        const hasValidWeights = tableData.filter((x: any) => x.dimensionId !== -1 && x.inTotal === 1).every((x: any) => x.weight);

        if (!hasValidWeights) {
            Toast.danger('请输入权重');
            return false;
        }

        // 检查权重总和
        if (tableData[tableData.length - 1].weight !== DIMENSION_VALIDATION_RULES.WEIGHT_SUM_TARGET) {
            Toast.danger('权重之和必须等于100%');
            return false;
        }
    }

    // 检查总题数
    if (tableData[tableData.length - 1].questionCount === 0) {
        Toast.danger('模板内总题数不能为0');
        return false;
    }

    return true;
}

// 不需要限制选择3个维度参与计算总分
export function validateDimensionParticipation(noComputedDimensionScore: number[], productId: number, tableData: Record<string, any>[]): boolean {
    if (!noComputedDimensionScore.includes(productId) && tableData.filter((x: any) => x.inTotal === 1).length < 3) {
        Toast.danger('至少选择3个维度参与计算总分');
        return false;
    }

    return true;
}
