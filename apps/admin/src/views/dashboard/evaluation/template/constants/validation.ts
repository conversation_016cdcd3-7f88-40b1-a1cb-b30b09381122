import { PCode } from '@crm/biz-exam-product';

// 不需要限制维度选择个数的产品列表
export const NO_COMPUTED_DIMENSION_SCORE = [
    PCode.FCA, // 金融人才测评产品
    PCode.PTA, // 16T职业性格测评产品
    PCode.OEA, // 五维性格
    PCode.MA, // 职业动机测评
    PCode.HOTS, // HOTS测评产品
    PCode.DISC, // 工作风格检验
    PCode.HIPO, // 敬业调研度
    PCode.ES, // 职业动机测评
    PCode.GPA, // 五维性格
    PCode.CAA, // 认知能力测评
    PCode.PQA, // 政治素质测评
    PCode.SRC, // 组合试卷
    PCode.NTA, // 新质人才测评方案
    PCode.OEA2, // 五维性格测评2.0
];

// 不需要进行权重检查的产品
export const NO_COMPUTED_DIMENSION_SCORE_MAP = [PCode.HMA, PCode.OEA, PCode.CRA, PCode.LPA, PCode.OEWA, PCode.PTA, PCode.OMHA, PCode.MA];

// 需要时间验证的产品
export const TIME_VALIDATION_PRODUCTS = [PCode.CRA, PCode.HIPO, PCode.ES, PCode.GPA, PCode.CAA, PCode.FCA, PCode.MA, PCode.OEA2];

// 需要领导力时间范围验证的产品
export const LEADERSHIP_TIME_RANGE_PRODUCTS = [PCode.LPA, PCode.PTA, PCode.OMHA, PCode.DISC];

// 需要覆盖维度验证的产品
export const COVERAGE_VALIDATION_PRODUCTS = [PCode.HIPO, PCode.ES, PCode.GPA];

// 验证错误消息
export const VALIDATION_MESSAGES = {
    COMBINATION_PAPER: '请配置试卷模版',
    DIMENSION_ORDER: '请输入维度顺序',
    DUPLICATE_ORDER: '顺序不允许重复',
    TIME_RANGE: '建议作答时间需要大于等于有效作答时间下限',
    LEADERSHIP_TIME_RANGE: '建议作答时间必须在有效作答时间范围内',
    WEIGHT_REQUIRED: '请输入权重',
    DIMENSION_COUNT_16T: '测评维度有且只能有4个',
    DIMENSION_COUNT_CHARACTER: '至少选择3个维度',
    POLITICAL_QUALITY_MIN: '最少选中4个二级维度，2个一级维度（不含质量监控维度）',
    POLITICAL_QUALITY_MAX: '最多包含20个二级维度，8个一级维度（不含质量监控维度）',
    PARTICIPATING_DIMENSION: '至少有一个参与总分的维度',
    WEIGHT_SUM: '权重之和必须等于100%',
    TOTAL_QUESTIONS: '模板内总题数不能为0',
    DIMENSION_PARTICIPATION: '至少选择3个维度参与计算总分',
} as const;

// 产品类型常量
export const PRODUCT_TYPES = {
    FINANCE_PERSONNEL: [PCode.FCA],
    MOTIVATION_ASSESSMENT: [PCode.MA],
    HOTS: [PCode.HOTS],
    HIPO: [PCode.HIPO],
    DEDICATION_RESEARCH: [PCode.ES],
    LEADERSHIP_SKILLS: [PCode.LPA],
    MENTAL_HEALTH_TEAM: [PCode.OMHA],
    MENTAL_HEALTH_TEAM_COMBINATION: [PCode.SRC],
    NEW_TALENT_COMBINATION: [PCode.NTA],
    PROFESSIONAL_16T: [PCode.PTA],
    CHARACTER: [PCode.OEA, PCode.OEWA],
    DISC: [PCode.DISC],
    HANDWRITE_PROFESSION: [PCode.GPA],
    CAA: [PCode.CAA],
    OEA2: [PCode.OEA2],
    POLITICAL_QUALITY: [PCode.PQA],
} as const;

// 维度验证规则
export const DIMENSION_VALIDATION_RULES = {
    MIN_PARTICIPATING: 3,
    MAX_16T_DIMENSIONS: 4,
    MIN_CHARACTER_DIMENSIONS: 3,
    POLITICAL_QUALITY_MIN_SECOND_LEVEL: 4,
    POLITICAL_QUALITY_MIN_FIRST_LEVEL: 2,
    POLITICAL_QUALITY_MAX_SECOND_LEVEL: 20,
    POLITICAL_QUALITY_MAX_FIRST_LEVEL: 8,
    WEIGHT_SUM_TARGET: 100,
} as const;
