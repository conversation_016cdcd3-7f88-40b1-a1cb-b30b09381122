import { PCode } from '@crm/biz-exam-product';

export const COLUMNS = [
    {
        label: '测评维度',
        field: 'dimensionName',
        ellipsis: {
            lineNum: 2,
            tooltip: true,
        },
        bodyCellStyle: {
            'word-break': 'break-all',
        },
    },
    {
        label: '是否参与总分',
        field: 'inTotal',
        width: 120,
    },
    {
        label: '维度展示名称',
        field: 'showName',
        width: 140,
    },
    {
        label: '维度顺序',
        field: 'rank',
        width: 120,
    },
    {
        label: '权重',
        field: 'weight',
        width: 120,
    },
    {
        label: '题目数',
        field: 'questionCount',
        width: 75,
    },
    {
        label: '建议作答时间',
        field: 'advise',
        width: 120,
    },
    {
        label: '有效作答时间范围',
        field: 'limit',
        width: 140,
    },
    {
        label: '有效作答时间下限',
        field: 'minLimit',
        width: 150,
    },
];
export const COLUMNS_FILTER_MAP: { [key: number]: any } = {
    [PCode.CA]: ['rank', 'showName', 'minLimit'],
    [PCode.FCA]: ['rank', 'showName', 'minLimit'],
    [PCode.GBA]: ['rank', 'showName', 'minLimit'],
    [PCode.HMA]: ['rank', 'showName', 'weight', 'limit'],
    [PCode.OEA]: ['rank', 'showName', 'weight', 'limit', 'inTotal'],
    [PCode.CRA]: ['rank', 'showName', 'weight', 'limit', 'advise', 'minLimit'],
    [PCode.LPA]: ['rank', 'showName', 'weight', 'limit', 'advise', 'minLimit'],
    [PCode.OEWA]: ['rank', 'showName', 'weight', 'limit', 'inTotal'],
    [PCode.PTA]: ['rank', 'showName', 'weight', 'limit', 'advise', 'minLimit', 'inTotal'],
    [PCode.OMHA]: ['rank', 'showName', 'weight', 'limit', 'advise', 'minLimit', 'inTotal'],
    [PCode.HIPO]: ['rank', 'showName', 'weight', 'limit', 'advise', 'minLimit', 'inTotal'],
    [PCode.ES]: ['rank', 'showName', 'weight', 'limit', 'advise', 'minLimit', 'inTotal'],
    [PCode.GPA]: ['rank', 'showName', 'weight', 'limit', 'advise', 'minLimit', 'inTotal'],
    [PCode.CAA]: ['limit', 'questionCount', 'advise', 'minLimit', 'inTotal'],
    [PCode.SRC]: ['limit', 'showName', 'questionCount', 'advise', 'minLimit', 'inTotal'],
    [PCode.NTA]: ['limit', 'showName', 'questionCount', 'advise', 'minLimit', 'inTotal'],
    [PCode.PQA]: ['inTotal', 'showName', 'rank', 'weight', 'limit'],
};
// [PCode.HMA, PCode.OEA, PCode.CRA, PCode.LPA, PCode.OEWA, PCode.PTA, PCode.OMHA, PCode.MA]
