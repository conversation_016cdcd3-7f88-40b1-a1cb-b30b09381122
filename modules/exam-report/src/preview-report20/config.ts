import type { ITableColumn, ILevelConfig } from './type/type';
import { LevelEnum } from './type/type';

export const dimensionListColumns: ITableColumn[] = [
    {
        field: 'showName',
        label: '一级维度',
        rowSpan: {
            width: '95px',
        },
        children: [
            {
                field: 'showName',
                label: '二级维度',
                width: '95px',
            },
            {
                field: 'definition',
                label: '维度说明',
                width: '498px',
                align: 'left',
            },
        ],
    },
];

export const levelMap: Record<LevelEnum, ILevelConfig> = {
    [LevelEnum.High]: {
        color: '#0092FA',
        backgroundColor: '#0092FA',
        tagBackgroundColor: 'radial-gradient(57% 57% at 50% 46%, #46BEFC 0%, #0092FA 100%)',
        cardBackgroundColor: 'rgba(0, 146, 250, 0.05)',
    },
    [LevelEnum.Medium]: {
        color: '#00A6A7',
        backgroundColor: '#49C7C7',
        tagBackgroundColor: 'radial-gradient(57% 57% at 50% 46%, #A3EBEB 0%, #49C7C7 100%)',
        cardBackgroundColor: 'rgba(73, 199, 199, 0.05)',
    },
    [LevelEnum.Low]: {
        color: '#DE952F',
        backgroundColor: '#E7A13E',
        tagBackgroundColor: 'radial-gradient(57% 57% at 50% 46%, #F6D19C 0%, #E7A13E 100%)',
        cardBackgroundColor: 'rgba(231, 161, 62, 0.05)',
    },
};

const getStyleValue = (code: LevelEnum, key: keyof ILevelConfig): string => {
    return levelMap[code || LevelEnum.Low]?.[key] || '';
};

export const getColor = (code: LevelEnum): string => getStyleValue(code, 'color');

export const getBackgroundColor = (code: LevelEnum): string => getStyleValue(code, 'backgroundColor');

export const getTagBackgroundColor = (code: LevelEnum): string => getStyleValue(code, 'tagBackgroundColor');

export const getCardBackgroundColor = (code: LevelEnum): string => getStyleValue(code, 'cardBackgroundColor');
