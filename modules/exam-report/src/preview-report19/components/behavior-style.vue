<template>
    <PageHeader />
    <SectionTitle title="行为风格" />
    <RichText v-if="data.behaviorStyleDescription" :domString="data.behaviorStyleDescription" :richTextIndex="5678" />
    <div class="behavior-style__description">
        <Card v-for="(item, index) in data.behaviorStyleGroups || []" :key="index" class="behavior-style__card">
            <div class="behavior-style__header">
                <SvgIcon :iconString="getIconString(item)" :width="149" :height="36"></SvgIcon>
                <div class="behavior-style__title">
                    <span v-for="(styleType, styleTypeIndex) in item.styleTypes || []" :key="styleTypeIndex">
                        {{ styleType.typeName }}
                    </span>
                </div>
            </div>
            <div class="behavior-style__main">
                <PointList :data="getFormatDescription(item)"></PointList>
                <div class="behavior-style__balance">
                    <div class="behavior-style__bar" :style="{ transform: `rotate(${getBalanceBarRotate(item)}deg)` }">
                        <div class="behavior-style__triangle behavior-style__triangle--left" :style="{ transform: `rotate(${-getBalanceBarRotate(item)}deg)` }">
                            <div class="behavior-style__triangle-inner"></div>
                            <div class="behavior-style__triangle-inner-bottom"></div>
                            <div :class="getCircleClassName(item, 0)" :style="{ background: getCircleColor(item, 0) }">
                                <span>{{ item.styleTypes[0].typePercentage }}%</span>
                            </div>
                        </div>
                        <SvgIcon :iconString="SvgBalanceBar" :width="131.42" :height="45.32" class="svg-balance-bar"></SvgIcon>
                        <div class="behavior-style__triangle behavior-style__triangle--right" :style="{ transform: `rotate(${-getBalanceBarRotate(item)}deg)` }">
                            <div class="behavior-style__triangle-inner"></div>
                            <div class="behavior-style__triangle-inner-bottom"></div>
                            <div :class="getCircleClassName(item, 1)" :style="{ background: getCircleColor(item, 1) }">
                                <span>{{ item.styleTypes[1].typePercentage }}%</span>
                            </div>
                        </div>
                    </div>
                    <SvgIcon :iconString="SvgBalance" :width="81.76" :height="145" class="svg-balance"></SvgIcon>
                </div>
            </div>
        </Card>
    </div>
    <Panel status="info" class="behavior-style__analysis">
        <template #header>
            <SvgIcon :iconString="SvgWorkplaceAdvantage" :width="110" :height="29"></SvgIcon>
        </template>
        <PointList :data="data.behaviorStyleGroups" valueKey="workplaceAdvantages"></PointList>
    </Panel>
    <Panel status="warning" class="behavior-style__analysis">
        <template #header>
            <SvgIcon :iconString="SvgWorkplaceDisadvantages" :width="110" :height="29"></SvgIcon>
        </template>
        <PointList :data="data.behaviorStyleGroups" valueKey="workplaceDisadvantages"></PointList>
    </Panel>
    <PageFooter :data="pageFooterData" />
</template>

<script setup lang="ts" name="BehaviorStyle">
import type { IData, IProps } from '../type/type';
import RichText from '@modules/exam-report/components/rich-text/index.vue';
import PageFooter from '@modules/exam-report/components/page-footer/index.vue';
import PageHeader from '@modules/exam-report/components/page-header/index.vue';
import SectionTitle from '@modules/exam-report/components/section-title/index.vue';
import SvgIcon from '@modules/exam-report/components/svg-icon/index.vue';
import SvgWorkplaceAdvantage from '../assets/workplace-advantages.svg?raw';
import SvgWorkplaceDisadvantages from '../assets/workplace-disadvantages.svg?raw';
import SvgBalance from '../assets/balance.svg?raw';
import SvgBalanceBar from '../assets/balance-bar.svg?raw';
import SvgActiveLeft from '../assets/active-left.svg?raw';
import SvgActiveRight from '../assets/active-right.svg?raw';
import Card from '@modules/exam-report/components/card/index.vue';
import Panel from '@modules/exam-report/components/panel/index.vue';
import PointListItem from '@modules/exam-report/components/point-list-item/index.vue';
import PointList from '@modules/exam-report/components/point-list/index.vue';
import { IBehaviorStyleGroupsItem } from '../type/type';
import { getCircleColor, getBalanceBarRotate } from '../utils/balance-bar';

withDefaults(defineProps<IProps>(), {
    data: () => ({}) as IData,
    pageFooterData: () => [],
});

// 获取图标字符串
function getIconString(item: IBehaviorStyleGroupsItem): string {
    return item.highLightType === item.styleTypes[0]?.typeName ? SvgActiveLeft : SvgActiveRight;
}

// 获取圆形类名
function getCircleClassName(item: IBehaviorStyleGroupsItem, index: number): string {
    const classList = ['behavior-style__triangle-text'];
    const isActive = item.styleTypes[index]?.typeName === item.highLightType;

    if (isActive) {
        classList.push('behavior-style__triangle-text--active');
    }

    return classList.join(' ');
}
function getFormatDescription(item: IBehaviorStyleGroupsItem): string[] {
    return item.typeDescription?.split('\n') || [];
}
</script>

<style lang="less" scoped>
.behavior-style {
    &__analysis {
        margin-bottom: var(--spacing-xlarge);
    }

    &__description {
        margin-top: 26px;
    }

    &__card {
        margin-bottom: 70px;
        &:nth-of-type(2) {
            margin-bottom: 28px;
        }
    }

    &__header {
        width: 100%;
        line-height: 36px;
        font-size: 13px;
        transform: translateY(-35px);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__title {
        line-height: var(--base-line-height-normal);
        color: var(--main-body-text-color);
        position: absolute;
        display: flex;
        justify-content: space-between;
        span {
            width: 82px;
            color: #fff;
            display: inline-block;
            text-align: center;
        }
    }

    &__main {
        min-height: 78px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    &__balance {
        position: absolute;
        right: 30px;
    }

    &__bar {
        transform-origin: center calc(~'100% - 12px');
        position: absolute;
        right: -26px;
        top: -1px;
    }

    &__triangle {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    &__triangle--left {
        transform-origin: center -4px;
        position: absolute;
        top: 40px;
        left: -18px;
    }

    &__triangle--right {
        transform-origin: center calc(~'100% - 68px');
        position: absolute;
        top: 40px;
        right: -16px;
    }

    &__triangle-inner {
        width: 0;
        height: 0;
        border-left: 24.75px solid transparent;
        border-right: 24.75px solid transparent;
        border-bottom: 41px solid #e9ecef;
    }

    &__triangle-inner-bottom {
        width: 48px;
        height: 24px;
        background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/zhice/report/357835589747196858.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
    }

    &__triangle-text {
        width: 32px;
        height: 32px;
        position: absolute;
        top: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        overflow: hidden;
        span {
            font-family: 'PingFang SC';
            background-color: #a2aab5;
            color: #fff;
            font-size: 10px;
            width: 29px;
            height: 29px;
            border: 2px solid #fff;
            border-radius: 50%;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    &__triangle-text--active {
        span {
            background-color: #0092fa;
        }
    }
}
</style>
