import type { ITableColumn, ILevelConfig } from './type/type';
import { LevelEnum } from './type/enums';

export const multiScanResultColumns: ITableColumn[] = [
    {
        field: 'thinkingStyle',
        label: '思维风格',
    },
    {
        field: 'behaviorStyle',
        label: '行为风格',
    },
    {
        field: 'communicationStyle',
        label: '沟通风格',
    },
    {
        field: 'managementPotentialLevel',
        label: '管理潜质',
    },
];

export const detailResultColumns: ITableColumn[] = [
    {
        field: 'dimensionName',
        label: '维度',
        width: '20%',
    },
    {
        field: 'dimensionScore',
        label: '得分描述',
        width: '80%',
    },
];

export const managementPotentialFactorColumns: ITableColumn[] = [
    {
        field: 'dimensionName',
        label: '管理潜质因素',
        width: '22%',
    },
    {
        field: 'levelDescription',
        label: '得分描述',
        width: '78%',
    },
];

export const managementAdviceDimensionLevelMap: Record<number, string> = {
    [LevelEnum.High]: '保持优势',
    [LevelEnum.RelativelyHigh]: '仍需努力',
    [LevelEnum.Medium]: '有成长空间',
    [LevelEnum.Low]: '有较大的发展空间',
};

export const referenceDataListColumns: ITableColumn[] = [
    {
        field: 'showName',
        label: '名称',
        width: '20%',
        align: 'left',
    },
    {
        field: 'result',
        label: '结果',
        width: '80%',
        align: 'left',
    },
];

export const radarChartBasePlateConfigColorList: { color: string; bgColor: string }[] = [
    {
        color: '#16B3B3',
        bgColor: '#BDF6F6',
    },
    {
        color: '#7046FA',
        bgColor: '#D7CCFF',
    },
    {
        color: '#56BD22',
        bgColor: '#D7F4CA',
    },
];

export const communicationStyleDistributionList = [
    {
        typeName: '竞争型',
        typeDesc: '专注于自己的\n观点被接受',
        value: 'competition',
        style: {
            margin: '0 7px 7px 0',
            borderRadius: '159px 0 0 0',
        },
        nameStyle: {
            transform: 'translate(46px, 52px)',
        },
        descStyle: {
            marginTop: '54px',
        },
    },
    {
        typeName: '合作型',
        typeDesc: '携手共进\n制定互利方案',
        value: 'cooperate',
        style: {
            margin: '0 0 7px 0',
            borderRadius: '0 159px 0 0',
        },
        nameStyle: {
            transform: 'translate(49px, 52px)',
        },
        descStyle: {
            marginTop: '54px',
        },
    },
    {
        typeName: '回避型',
        typeDesc: '退一步海阔天空',
        value: 'avoid',
        style: {
            margin: '0 7px 0 0',
            borderRadius: '0 0 0 159px',
        },
        nameStyle: {
            transform: 'translate(47px, 40px)',
        },
    },
    {
        typeName: '顺应型',
        typeDesc: '接受他人的观点',
        value: 'compliance',
        style: {
            margin: '0 0 0 0',
            borderRadius: '0 0 159px 0',
        },
        nameStyle: {
            transform: 'translate(50px, 40px)',
        },
    },
];

export const levelMap: Record<LevelEnum, ILevelConfig> = {
    [LevelEnum.High]: {
        color: '#0092FA',
        backgroundColor: '#E8F7FF',
        scoreBackgroundColor: '#0092FA',
        status: 'info',
    },
    [LevelEnum.RelativelyHigh]: {
        color: '#00A6A7',
        backgroundColor: '#E4F9F9',
        scoreBackgroundColor: '#49C7C7',
        status: 'success',
    },
    [LevelEnum.Medium]: {
        color: '#DE952F',
        backgroundColor: '#FFF5E7',
        scoreBackgroundColor: '#E7A13E',
        status: 'warning',
    },
    [LevelEnum.Low]: {
        color: '#F24955',
        backgroundColor: '#FFF0F1',
        scoreBackgroundColor: '#E97570',
        status: 'danger',
    },
};

export const getColor = (code: LevelEnum): string => {
    return levelMap[code || 1]?.color || '';
};

export const getStatus = (code: LevelEnum): 'info' | 'success' | 'danger' | 'warning' | undefined => {
    return levelMap[code || 1]?.status;
};
