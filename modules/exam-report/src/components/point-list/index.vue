<template>
    <ol class="point-list">
        <PointListItem v-for="(item, index) in data" :key="index">
            {{ valueKey ? item[valueKey] : item }}
        </PointListItem>
    </ol>
</template>

<script setup lang="ts" name="ExamReportPointList">
import { Props } from './props';
import PointListItem from '../point-list-item/index.vue';

withDefaults(defineProps<Props>(), {
    data: () => [],
    valueKey: '',
});
</script>

<style lang="less" scoped>
.point-list {
    display: flex;
    flex-direction: column;
}
</style>
