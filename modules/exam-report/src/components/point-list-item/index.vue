<template>
    <li v-if="$slots.default" :class="classes">
        <slot></slot>
    </li>
</template>

<script setup lang="ts" name="ExamReportPointListItem">
import { computed } from 'vue';
import { Props } from './props';

const propsData = withDefaults(defineProps<Props>(), {});

const classes = computed(() => {
    return [
        'point-list__item',
        {
            [`point-list__item--${propsData.align}`]: propsData.align,
        },
    ];
});
</script>

<style lang="less" scoped>
.point-list__item {
    width: 100%;
    list-style: none;
    line-height: var(--report-base-line-height-normal);
    min-height: var(--report-base-line-height-normal);
    padding-left: var(--spacing-medium);
    position: relative;
    white-space: pre-wrap;
    &::before {
        content: url('https://img.bosszhipin.com/static/zhipin/kanjian/zhice/report/376099462847158886.svg');
        line-height: var(--report-base-line-height-normal);
        width: 4px;
        height: 4px;
        overflow: hidden;
        position: absolute;
        left: 0;
        top: 11px;
    }
    &--left {
        text-align: left;
    }
    &--center {
        text-align: center;
    }
    &--right {
        text-align: right;
    }
}
</style>
