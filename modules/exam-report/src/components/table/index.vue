<template>
    <div :class="tableHeaderClasses">
        <slot name="header">
            <div v-for="item of columnList" :key="item.field" class="report__table-td" :style="getHeaderStyle(item)">
                {{ item.label }}
            </div>
        </slot>
    </div>
    <div v-for="(row, index) of tableData" :key="index" :class="tableBodyClasses">
        <div v-for="column of columns" :key="column.field" :class="tableRowClasses(column)" :style="{ width: column.width ? column.width : `${100 / columns.length}%` }">
            <div class="report__table-td-content" :style="{ width: column.rowSpan?.width ? column.rowSpan?.width : '100%' }">
                <slot :name="`td-${column.field}`" :row="row">{{ row[column.field] }}</slot>
            </div>
            <div v-if="column.children && row.children?.length > 0" class="report__table-row-children">
                <div v-for="subRow of row.children" :key="subRow.field" class="report__table-td-children">
                    <div
                        v-for="child of column.children || []"
                        :key="child.field"
                        :style="{ width: child.width ? child.width : `${100 / columns.length}%` }"
                        :class="getChildrenRowClass(child.align)"
                    >
                        <slot :name="`td-${child.field}`" :row="child">{{ subRow[child.field] }}</slot>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="ExamReportTable">
import { computed } from 'vue';
import { Props, IColumn } from './props';

const propsData = withDefaults(defineProps<Props>(), {
    columns: () => [],
    tableData: () => [],
    border: true,
    type: 'normal',
});

const columnList = computed(() => {
    return propsData.columns.reduce((acc, item) => {
        acc.push(item);
        if (item.children) {
            acc.push(...item.children);
        }
        return acc;
    }, [] as IColumn[]);
});

const tableHeaderClasses = computed(() => {
    return [
        'report__table-header',
        {
            'report__table-header--none-border': propsData.border === false,
            'report__table-header--simple': propsData.type === 'simple',
        },
    ];
});

const tableBodyClasses = computed(() => {
    return [
        'report__table-row',
        {
            'report__table-row--none-border': propsData.border === false,
            'report__table-row--simple': propsData.type === 'simple',
        },
    ];
});

function tableRowClasses(row: any) {
    const align = row.align || 'center';
    return [
        'report__table-tbody-td',
        {
            [`report__table-td--${align}`]: align,
        },
    ];
}

function getChildrenRowClass(align?: string) {
    return [
        'report__table-td-content',
        {
            [`report__table-td-content--${align}`]: align,
        },
    ];
}

function getHeaderStyle(item: IColumn) {
    const width = item.rowSpan?.width || item.width;
    return {
        width: width ? width : `${100 / propsData.columns.length}%`,
        textAlign: item.align || 'center',
    };
}
</script>

<style lang="less" scoped>
.report {
    &__table-header {
        background-color: var(--tag-info-color-5);
        display: flex;
        border-radius: var(--report-border-radius-xlarge) var(--report-border-radius-xlarge) 0 0;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: var(--report-base-font-size-normal);
        border-color: var(--border-color);
        border-width: 1px;
        border-style: solid;
        border-bottom: none;
        .report__table-td {
            border-bottom: none;
            padding-left: var(--spacing-normal);
            padding-right: var(--spacing-normal);
            padding-top: var(--spacing-normal);
            padding-bottom: var(--spacing-normal);
            border-right-color: var(--border-color);
            border-right-width: 1px;
            border-right-style: solid;
            &:last-child {
                border-right-width: 0px;
            }
        }
    }

    &__table-tbody-td {
        display: flex;
        border-right-color: var(--border-color);
        border-right-width: 1px;
        border-right-style: solid;
        &:last-child {
            border-right-width: 0px;
        }
        .report__table-td {
            width: 100%;
            display: flex;
        }

        .report__table-td-content {
            width: 100%;
            padding-left: var(--spacing-normal);
            padding-right: var(--spacing-normal);
            padding-top: var(--spacing-normal);
            padding-bottom: var(--spacing-normal);
            display: flex;
            align-items: center;
            &:last-child {
                border-right-width: 0px;
            }
        }

        .report__table-td-children {
            display: flex;
            border-left-width: 1px;
            border-left-style: solid;
            border-left-color: var(--border-color);
            .report__table-td-content {
                border-right-width: 1px;
                border-right-style: solid;
                border-right-color: var(--border-color);
                border-bottom-width: 1px;
                border-bottom-style: solid;
                border-bottom-color: var(--border-color);
                &:last-child {
                    border-right-width: 0px;
                }
            }
            .report__table-td-content--left {
                justify-content: flex-start;
            }
            .report__table-td-content--right {
                justify-content: flex-end;
            }
            &:last-child {
                .report__table-td-content {
                    border-bottom-width: 0px;
                }
            }
        }
    }

    &__table-td--left {
        .report__table-td-content {
            justify-content: flex-start;
        }
    }

    &__table-td--center {
        .report__table-td-content {
            justify-content: center;
        }
    }

    &__table-td--right {
        .report__table-td-content {
            justify-content: flex-end;
        }
    }

    &__table-row {
        display: flex;
        width: 100%;
        border-width: 1px;
        border-style: solid;
        border-color: var(--border-color);
        border-top-width: 0px;
    }

    &__table-td {
        line-height: var(--report-base-line-height-normal);
    }

    &__table-header--none-border {
        border-width: 0px;
        .report__table-td {
            border-width: 0px;
        }
    }

    &__table-row--none-border {
        border-width: 0px;
        .report__table-td {
            border-width: 0px;
        }
    }

    &__table-header--simple {
        background-color: var(--base-background-color);
        font-size: var(--report-base-font-size-normal);
        border-radius: var(--border-radius-none);
        border-width: 0px;
        font-family: var(--FZLanTingHeiS-R-GB);
        gap: var(--spacing-small);
        padding-bottom: var(--spacing-small);
        .report__table-td {
            background-color: var(--gray-color-2);
            border-width: 0px;
            padding-left: var(--spacing-none);
            padding-right: var(--spacing-none);
            padding-top: var(--spacing-none);
            padding-bottom: var(--spacing-none);
        }
    }

    &__table-row--simple {
        font-size: var(--report-base-font-size-normal);
        border-width: 0px;
        border-radius: var(--border-radius-none);

        .report__table-td-content {
            border-width: 0px;
            padding-left: var(--spacing-small);
            padding-right: var(--spacing-small);
            padding-top: var(--spacing-small);
            padding-bottom: var(--spacing-none);
        }

        .report__table-tbody-td {
            border-right-width: 0px;
        }
    }
}
</style>
