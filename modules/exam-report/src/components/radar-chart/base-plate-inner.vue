<template>
    <div class="base-plate__inner-wrap">
        <template v-if="list.length > 0">
            <div v-for="(item, index) in list" :key="index" class="base-plate__inner-item" :style="getRadarAxisInnerStyle(index)">
                <div class="base-plate__inner-label" :style="getRadarAxisInnerLabelStyle(index)">
                    {{ item[valueKey] }}
                </div>
                <div class="base-plate__inner-score" :style="getRadarAxisInnerLabelStyle(index)">{{ item.dimensionScore }}</div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts" name="BasePlate">
import type { BasePlateInnerProps } from './props';

const propsData = withDefaults(defineProps<BasePlateInnerProps>(), {
    valueKey: 'dimensionName',
    list: () => [],
});

function getRadarAxisInnerStyle(index: number) {
    return {
        transform: `rotate(${(index - 1) * (360 / propsData.list.length)}deg)`,
    };
}

function getRadarAxisInnerLabelStyle(index: number) {
    if (index >= 4 && index <= 7) {
        return {
            transform: `rotate(180deg)`,
        };
    }
    return {};
}
</script>

<style scoped lang="less">
.base-plate {
    &__inner-wrap {
        width: 348px;
        height: 348px;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 16px;
    }

    &__inner-item {
        width: 318px;
        height: 318px;
        text-align: center;
        color: var(--main-body-text-color);
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: var(--font-size-body-2);
        line-height: 20px;
        position: absolute;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        &:after {
            content: '';
            display: block;
            width: 1px;
            height: 101px;
            background-color: #d4d4d4;
        }
    }

    &__inner-label {
        display: inline-block;
    }

    &__inner-score {
        width: 30px;
        height: 30px;
        color: var(--main-body-text-color);
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: 12px;
        line-height: 28px;
        border-radius: 50%;
        border: 1.5px solid #e1ebeb;
        text-align: center;
        background-color: var(--base-background-color);
        margin-top: 8px;
        overflow: hidden;
    }
}
</style>
