<template>
    <div class="base-plate__svg-wrap">
        <svg width="200" height="200" class="base-plate__svg">
            <!-- 绘制雷达图的圆形网格线 -->
            <circle
                v-for="(item, index) in circleConfig.radiiList"
                :key="index"
                :cx="circleConfig.cx"
                :cy="circleConfig.cy"
                :r="item"
                :stroke="circleConfig.stroke"
                :stroke-width="circleConfig.strokeWidth"
                :fill="circleConfig.fill"
            />
            <!-- 绘制坐标轴 -->
            <g v-for="(e, i) in list" :key="i" :transform="`rotate(${(i - 1) * (360 / list.length)} ${lineConfig.x1} ${lineConfig.y1})`">
                <circle :cx="calculateX(i, e[valueKey])" :cy="calculateY(i, e[valueKey])" :r="lineConfig.r" :fill="lineConfig.fill" :fill-opacity="lineConfig.fillOpacity" />
            </g>
            <!-- 绘制连线，假设这里有n个数据点 -->
            <path :d="generatePath()" :stroke="pathConfig.stroke" :stroke-width="pathConfig.strokeWidth" :fill="pathConfig.fill" :fill-opacity="pathConfig.fillOpacity" />
        </svg>
    </div>
</template>

<script setup lang="ts" name="SvgRegion">
import type { SvgRegionProps } from './props';

const propsData = withDefaults(defineProps<SvgRegionProps>(), {
    list: () => [],
    circleConfig: () => ({
        cx: 100,
        cy: 100,
        stroke: '#e5eded',
        strokeWidth: 1,
        fill: 'none',
        radiiList: [6, 18, 30, 42, 54, 66, 78, 90, 100],
    }),
    lineConfig: () => ({
        fill: 'rgba(0, 190, 189)',
        fillOpacity: 0.1,
        stroke: 'rgba(0, 190, 189)',
        strokeWidth: 1,
        r: 2,
        x1: 100,
        y1: 100,
        x2: 100,
        y2: 0,
    }),
    pathConfig: () => ({
        stroke: '#00BEBD',
        strokeWidth: 1,
        fill: 'rgba(0, 190, 189)',
        fillOpacity: 0.1,
    }),
    valueKey: 'dimensionScore',
});

function calculateX(i: number, score: number) {
    const angle = (i - 1) * (360 / propsData.list?.length) * (Math.PI / 180);
    const radius = score; // 半径
    return Number(propsData.circleConfig.cx) + radius * Math.sin(angle);
}

function calculateY(i: number, score: number) {
    const angle = (i - 1) * (360 / propsData.list?.length) * (Math.PI / 180);
    const radius = score; // 半径
    return Number(propsData.circleConfig.cy) - radius * Math.cos(angle);
}

function generatePath() {
    let path = '';
    for (let i = 0; i <= propsData.list?.length - 1; i++) {
        const item = propsData.list?.[i];
        const dimensionScore = (item || [])[propsData.valueKey] || 0;
        const x = calculateX(i, dimensionScore);
        const y = calculateY(i, dimensionScore);

        if (i === 0) {
            path += `M ${x} ${y}`; // 移动到第一个点
        } else {
            path += ` L ${x} ${y}`; // 从当前点绘制直线到下一个点
        }
    }
    path += ' Z'; // 闭合路径
    return path;
}
</script>

<style scoped lang="less">
.base-plate {
    &__svg-wrap {
        width: 224px;
        height: 224px;
        background-color: var(--base-background-color);
        border-radius: 50%;
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &__svg {
        position: absolute;
        z-index: 1;
    }
}
</style>
