<template>
    <div class="overview-title-container">
        <div class="title-wrap">
            <div class="title-text">
                <div class="text-inner" ref="titleTextRef">{{ name }}</div>
            </div>
        </div>
        <div class="tick-wrap">
            <div class="tick-item" v-for="(item, index) of splitArray" :key="index" :data-number="item">{{ item }}</div>
            <div class="tick-bottom"></div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';

defineOptions({
    name: 'OverviewTitle',
});
const props = defineProps({
    name: {
        type: String,
        default: '',
    },
});

const splitArray = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
const titleTextRef = ref();
const lineHeight = 20;
const skewX = ref('28deg');
onMounted(() => {
    const height = titleTextRef.value.offsetHeight;
    switch (height) {
        case 1 * lineHeight:
            skewX.value = '28deg';
            break;
        case 2 * lineHeight:
            skewX.value = '19deg';
            break;
        case 3 * lineHeight:
            skewX.value = '14deg';
            break;
        case 4 * lineHeight:
            skewX.value = '11deg';
            break;

        default:
            skewX.value = '0deg';
            break;
    }
});
</script>
<style lang="less" scoped>
.overview-title-container {
    background: #e8f7ff;
    border-radius: 2px;
    overflow: hidden;
    display: flex;
    position: relative;
    .title-wrap {
        position: relative;
        max-width: 155px;
        display: flex;
        flex-direction: row;
        .title-text {
            padding: 8px 8px 8px 12px;
            color: #fff;
            background-color: #0092fa;
            font-family: var(--FZLanTingHeiS-DB-GB);
            font-size: 15px;
            line-height: 20px;
            min-width: 140px;
            .text-inner {
                white-space: pre-wrap;
                word-break: break-all;
            }
        }
        &::after {
            content: '';
            min-width: 65px;
            height: 100%;
            background-image: url(https://img.bosszhipin.com/static/zhipin/kanjian/zhice/report/623798439847158065.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .tick-wrap {
        position: absolute;
        display: flex;
        gap: 11px;
        left: 257.5px;
        bottom: 10px;

        --item-width: 22px;
        --item-height: 3px;
        --tick-width: 1px;
        --margin: 6px;

        .tick-item {
            width: var(--item-width);
            text-align: center;
            color: #1f1f1f;
            font-size: 12px;
            line-height: 14px;
            position: relative;
            &::after {
                content: '';
                width: var(--tick-width);
                background-color: #cccccc;
                height: var(--item-height);
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                top: calc(100% + var(--margin));
            }
        }
        .tick-bottom {
            position: absolute;
            height: 1px;
            background-color: #cccccc;
            left: calc(var(--item-width) / 2 - var(--tick-width) / 2);
            right: calc(var(--item-width) / 2 - var(--tick-width) / 2);
            top: calc(100% + var(--margin) + var(--item-height));
        }
    }
}
</style>
